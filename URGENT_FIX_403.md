# 🚨 URGENT: แก้ไข 403 Error ทันที

## ปัญหา
```
[core:crit] (13)Permission denied: AH00529: /var/www/vhosts/nkslgroup.com/httpdocs/container_system/.htaccess pcfg_openfile: unable to check htaccess file, ensure it is readable and that '/var/www/vhosts/nkslgroup.com/httpdocs/container_system/' is executable
```

## สาเหตุ
1. Directory `/var/www/vhosts/nkslgroup.com/httpdocs/container_system/` ไม่มี execute permission
2. ไฟล์ .htaccess อ่านไม่ได้

## วิธีแก้ไขทันที

### ขั้นตอนที่ 1: แก้ไข Permissions ผ่าน SSH
```bash
# เข้าไปในโฟลเดอร์ container_system
cd /var/www/vhosts/nkslgroup.com/httpdocs/container_system/

# แก้ไข permissions ของ root directory
chmod 755 .

# แก้ไข permissions ของ directories ทั้งหมด
find . -type d -exec chmod 755 {} \;

# แก้ไข permissions ของ files ทั้งหมด
find . -type f -exec chmod 644 {} \;

# แก้ไข .htaccess files โดยเฉพาะ
find . -name ".htaccess" -exec chmod 644 {} \;
```

### ขั้นตอนที่ 2: หรือใช้ Script
```bash
# รัน script ที่เตรียมไว้
chmod +x fix_permissions.sh
./fix_permissions.sh
```

### ขั้นตอนที่ 3: ทดสอบ
```
https://nkslgroup.com/container_system/public/test.php
```

## หากใช้ Plesk Control Panel

### 1. เข้า File Manager
- เข้า Plesk Control Panel
- ไป File Manager
- เข้าโฟลเดอร์ `httpdocs/container_system/`

### 2. แก้ไข Permissions
- คลิกขวาที่โฟลเดอร์ `container_system`
- เลือก "Change Permissions"
- ตั้งค่า: `755` (rwxr-xr-x)

### 3. แก้ไข Permissions ของไฟล์ทั้งหมด
- เลือกไฟล์ทั้งหมด
- คลิกขวา > "Change Permissions"
- ตั้งค่า: `644` (rw-r--r--)

### 4. แก้ไข Permissions ของ Directories
- เลือก directories ทั้งหมด (public, includes, database, logs)
- คลิกขวา > "Change Permissions"  
- ตั้งค่า: `755` (rwxr-xr-x)

## หากใช้ cPanel

### 1. เข้า File Manager
- เข้า cPanel
- เปิด File Manager
- ไปที่ `public_html/container_system/`

### 2. แก้ไข Permissions
- คลิกขวาที่โฟลเดอร์ `container_system`
- เลือก "Change Permissions"
- ตั้งค่า: `755`

### 3. แก้ไขไฟล์และโฟลเดอร์ย่อย
- เลือกทั้งหมด
- แก้ไข permissions ตามตาราง

## ตาราง Permissions ที่ถูกต้อง

| Type | Path | Permission | Octal |
|------|------|------------|-------|
| Directory | container_system/ | rwxr-xr-x | 755 |
| Directory | public/ | rwxr-xr-x | 755 |
| Directory | includes/ | rwxr-xr-x | 755 |
| Directory | database/ | rwxr-xr-x | 755 |
| Directory | logs/ | rwxr-xr-x | 755 |
| File | *.php | rw-r--r-- | 644 |
| File | .htaccess | rw-r--r-- | 644 |
| File | *.sql | rw-r--r-- | 644 |

## การทดสอบหลังแก้ไข

### 1. ทดสอบไฟล์พื้นฐาน
```
https://nkslgroup.com/container_system/public/test.php
```

### 2. ทดสอบ Login Page
```
https://nkslgroup.com/container_system/public/login.php
```

### 3. ทดสอบ Redirect
```
https://nkslgroup.com/container_system/
```

## หากยังไม่ได้

### 1. ลบ .htaccess ชั่วคราว
```bash
# เปลี่ยนชื่อ .htaccess ใน root
mv .htaccess .htaccess_backup

# เปลี่ยนชื่อ .htaccess ใน public
mv public/.htaccess public/.htaccess_backup
```

### 2. ทดสอบอีกครั้ง
```
https://nkslgroup.com/container_system/public/login.php
```

### 3. หากทำงาน ให้เปิด .htaccess ทีละไฟล์
```bash
# เปิด public/.htaccess ก่อน
mv public/.htaccess_backup public/.htaccess

# ทดสอบ
# หากทำงาน ให้เปิด root .htaccess
mv .htaccess_backup .htaccess
```

## ติดต่อ Support

หากยังแก้ไขไม่ได้ ให้ติดต่อ hosting provider พร้อมข้อมูล:

1. **Error Message**: Permission denied AH00529
2. **Path**: `/var/www/vhosts/nkslgroup.com/httpdocs/container_system/`
3. **Required**: Directory execute permission (755)
4. **Required**: .htaccess read permission (644)
