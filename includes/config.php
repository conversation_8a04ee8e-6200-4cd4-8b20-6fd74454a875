<?php
/**
 * Configuration file for Container System
 * Production Environment Settings
 */

// Environment setting
define('ENVIRONMENT', 'production'); // 'development' or 'production'

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'container_system');
define('DB_USER', 'nksl_consys');        // เปลี่ยนเป็น username ของ database จริง
define('DB_PASS', 'HMqB2*lxpg3f~a9w'); // เปลี่ยนเป็น password ที่ปลอดภัย
define('DB_CHARSET', 'utf8mb4');

// Security Settings
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes in seconds

// Application Settings
define('APP_NAME', 'Container System');
define('APP_VERSION', '2.0');
define('TIMEZONE', 'Asia/Bangkok');

// File Upload Settings
define('MAX_FILE_SIZE', 5242880); // 5MB in bytes
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);

// Email Settings (if needed)
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '');
define('FROM_NAME', 'Container System');

// Error Logging
define('LOG_ERRORS', true);
define('LOG_FILE', '../logs/error.log');

// Development vs Production settings
if (ENVIRONMENT === 'development') {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_FILE);
}

// Set timezone
date_default_timezone_set(TIMEZONE);

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1); // Set to 1 if using HTTPS
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');
?>
