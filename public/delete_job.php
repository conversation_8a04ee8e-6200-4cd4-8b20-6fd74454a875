<?php
// Production mode - disable error display
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

// ตรวจสอบการล็อกอิน
requireLogin();

// ตรวจสอบว่าเป็น admin เท่านั้น
requireAdmin();

// ตรวจสอบว่ามี job ID หรือไม่
if (!isset($_GET["id"])) {
    $_SESSION["error"] = "No job ID provided";
    header("Location: job_list.php");
    exit;
}

$job_id = $_GET["id"];

// ดึงข้อมูล job เพื่อตรวจสอบและแสดงข้อมูล
$stmt = $pdo->prepare("SELECT j.*, u.username FROM jobs j JOIN users u ON u.id = j.user_id WHERE j.id = ?");
$stmt->execute([$job_id]);
$job = $stmt->fetch();

if (!$job) {
    $_SESSION["error"] = "Job not found";
    header("Location: job_list.php");
    exit;
}

// ประมวลผลการลบ
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['confirm_delete'])) {
    // ตรวจสอบ CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $_SESSION["error"] = "Invalid form submission";
        header("Location: job_list.php");
        exit;
    }
    
    try {
        // เริ่ม transaction
        $pdo->beginTransaction();
        
        // บันทึกประวัติการลบ
        $stmt = $pdo->prepare("INSERT INTO job_edit_history 
            (job_id, user_id, username, field_name, old_value, new_value, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->execute([
            $job_id,
            $_SESSION["user_id"],
            $_SESSION["username"],
            'DELETED',
            'Job existed',
            'Job deleted by admin',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        // ลบ job (ประวัติการแก้ไขจะถูกลบตาม CASCADE)
        $stmt = $pdo->prepare("DELETE FROM jobs WHERE id = ?");
        $stmt->execute([$job_id]);
        
        // Commit transaction
        $pdo->commit();
        
        $_SESSION["success"] = "Job #{$job['job_number']} deleted successfully";
        header("Location: job_list.php");
        exit;
        
    } catch (Exception $e) {
        // Rollback transaction
        $pdo->rollback();
        $_SESSION["error"] = "Error deleting job: " . $e->getMessage();
        header("Location: job_list.php");
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Delete Job - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">Container System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
                </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav ms-auto">
                <?php if (isset($_SESSION["username"])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </li>
                <?php else: ?>
                <li class="nav-item">
                    <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block">
            <div class="sidebar p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="job_list.php">
                            <i class="fas fa-list"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create_job.php">
                            <i class="fas fa-plus"></i> Create Job
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user_list.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <div class="col-md-9 col-lg-10">
            <div class="content">
                <h2><i class="fas fa-trash text-danger"></i> Delete Job</h2>
                
                <div class="card danger-zone">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle"></i> Danger Zone - Admin Only</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong>Warning!</strong> This action cannot be undone. Deleting this job will permanently remove all associated data and edit history.
                        </div>
                        
                        <h6>Job Details:</h6>
                        <table class="table table-bordered">
                            <tr><th>Job Number:</th><td><?= htmlspecialchars($job['job_number']) ?></td></tr>
                            <tr><th>Customer:</th><td><?= htmlspecialchars($job['customer_name']) ?></td></tr>
                            <tr><th>Job Title:</th><td><?= htmlspecialchars($job['job_title']) ?></td></tr>
                            <tr><th>Container No:</th><td><?= htmlspecialchars($job['container_no']) ?></td></tr>
                            <tr><th>Status:</th><td><?= htmlspecialchars($job['status']) ?></td></tr>
                            <tr><th>Created By:</th><td><?= htmlspecialchars($job['username']) ?></td></tr>
                            <tr><th>Created At:</th><td><?= htmlspecialchars($job['created_at']) ?></td></tr>
                        </table>
                        
                        <form method="post" class="mt-4">
                            <?= generateCSRFToken() ?>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirm_checkbox" required>
                                <label class="form-check-label text-danger" for="confirm_checkbox">
                                    I understand that this action is permanent and cannot be undone
                                </label>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" name="confirm_delete" class="btn btn-danger" id="delete_btn" disabled>
                                    <i class="fas fa-trash"></i> Permanently Delete Job
                                </button>
                                <a href="job_list.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.getElementById('confirm_checkbox').addEventListener('change', function() {
    document.getElementById('delete_btn').disabled = !this.checked;
});
</script>
</body>
</html>
<?php include '../includes/footer.php'; ?>
