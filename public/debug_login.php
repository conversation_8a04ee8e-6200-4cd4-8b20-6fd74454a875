<?php
// Debug login issues
echo "<h1>🔍 Login Debug Tool</h1>";

try {
    require_once '../includes/config.php';
    echo "<p style='color: green;'>✅ Config loaded successfully</p>";
    
    require_once '../includes/db.php';
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    // ตรวจสอบตาราง users
    echo "<h2>📊 Users Table Info</h2>";
    
    // นับจำนวน users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetchColumn();
    echo "<p><strong>Total users:</strong> " . $count . "</p>";
    
    // แสดงข้อมูล users ทั้งหมด
    echo "<h3>👥 All Users:</h3>";
    $stmt = $pdo->query("SELECT id, username, role, created_at FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ No users found in database!</p>";
        echo "<p>Database is empty. Need to create admin user.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Created At</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ admin user โดยเฉพาะ
    echo "<h3>🔐 Admin User Check:</h3>";
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "<p style='color: red;'>❌ Admin user not found!</p>";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>🛠️ Fix: Create Admin User</h4>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='create_admin' value='1'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Create Admin User</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<p style='color: green;'>✅ Admin user found!</p>";
        echo "<p><strong>ID:</strong> " . $admin['id'] . "</p>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($admin['username']) . "</p>";
        echo "<p><strong>Role:</strong> " . htmlspecialchars($admin['role']) . "</p>";
        echo "<p><strong>Password Hash:</strong> " . htmlspecialchars($admin['password_hash']) . "</p>";
        
        // ทดสอบ password
        echo "<h4>🧪 Password Test:</h4>";
        $test_password = 'admin123';
        $verify_result = password_verify($test_password, $admin['password_hash']);
        
        if ($verify_result) {
            echo "<p style='color: green;'>✅ Password 'admin123' is CORRECT</p>";
        } else {
            echo "<p style='color: red;'>❌ Password 'admin123' is INCORRECT</p>";
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>🛠️ Fix: Update Password</h4>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='fix_password' value='1'>";
            echo "<button type='submit' style='background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 5px;'>Fix Admin Password</button>";
            echo "</form>";
            echo "</div>";
        }
    }
    
    // Handle form submissions
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['create_admin'])) {
            echo "<h3>🔧 Creating Admin User...</h3>";
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, password_hash, role, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute(['admin', $password_hash, 'admin']);
            echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "<p><a href='login.php'>Go to Login</a></p>";
        }
        
        if (isset($_POST['fix_password'])) {
            echo "<h3>🔧 Fixing Admin Password...</h3>";
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
            $stmt->execute([$password_hash]);
            echo "<p style='color: green;'>✅ Admin password fixed!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "<p><a href='login.php'>Go to Login</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    
    // ตรวจสอบว่าตารางมีอยู่หรือไม่
    echo "<h3>🔍 Database Structure Check:</h3>";
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tables)) {
            echo "<p style='color: red;'>❌ No tables found! Database not imported.</p>";
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>🚨 Action Required:</h4>";
            echo "<p>Import the database schema:</p>";
            echo "<code>mysql -u nksl_consys -p container_system < database/container_system_mariadb55.sql</code>";
            echo "</div>";
        } else {
            echo "<p style='color: green;'>✅ Tables found:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>" . $table . "</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e2) {
        echo "<p style='color: red;'>Cannot check database structure: " . $e2->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<h3>🔗 Quick Links:</h3>";
echo "<p><a href='login.php'>Login Page</a> | <a href='test.php'>Database Test</a> | <a href='simple_test.php'>PHP Test</a></p>";

echo "<p style='color: #856404; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin-top: 20px;'>";
echo "⚠️ <strong>Security:</strong> Delete this file after fixing the issue!";
echo "</p>";
?>
