<?php
// แสดงข้อ
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

// ตรวจสอบการล็อกอิน
requireLogin();

// Save customer names, job titles, and places for autocomplete
function saveForAutocomplete($table, $field, $value) {
    global $pdo;
    if (empty($value)) return;
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO {$table} ({$field}) VALUES (?)");
    $stmt->execute([$value]);
}

// Validation function
function validateJobForm($data) {
    $errors = [];

    // Required fields validation
    if (empty(trim($data["customer_name"]))) {
        $errors[] = "Customer name is required";
    }

    if (empty(trim($data["job_title"]))) {
        $errors[] = "Job title is required";
    }

    if (empty(trim($data["container_no"]))) {
        $errors[] = "Container number is required";
    } elseif (!preg_match('/^[A-Z]{4}[0-9]{7}$/', $data["container_no"])) {
        $errors[] = "Container number must be in format ABCD1234567 (4 letters + 7 numbers)";
    }

    if (empty($data["container_size"])) {
        $errors[] = "Container size is required";
    } elseif (!in_array($data["container_size"], ['20', '40'])) {
        $errors[] = "Container size must be 20 or 40";
    }

    if (empty(trim($data["return_place"]))) {
        $errors[] = "Return place is required";
    }

    if (empty($data["container_type"])) {
        $errors[] = "Container type is required";
    } elseif (!in_array($data["container_type"], ['เปล่าสั้น', 'เปล่ายาว', 'หนักสั้น', 'หนักยาว'])) {
        $errors[] = "Container type must be 'เปล่าสั้น', 'เปล่ายาว', 'หนักสั้น', or 'หนักยาว'";
    }

    // Date validations
    if (!empty($data["job_date"]) && !strtotime($data["job_date"])) {
        $errors[] = "Invalid job date format";
    }

    if (!empty($data["first_return_date"]) && !strtotime($data["first_return_date"])) {
        $errors[] = "Invalid first return date format";
    }

    if (!empty($data["closing_date"]) && !strtotime($data["closing_date"])) {
        $errors[] = "Invalid closing date format";
    }

    if (!empty($data["deposit_date"]) && !strtotime($data["deposit_date"])) {
        $errors[] = "Invalid deposit date format";
    }

    // Container number uniqueness check
    global $pdo;
    if (!empty($data["container_no"])) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM jobs WHERE container_no = ? AND status = 'Pending'");
        $stmt->execute([$data["container_no"]]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = "Container number already exists in pending jobs";
        }
    }

    return $errors;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug: Log form submission
    error_log("Form submitted with CSRF token: " . ($_POST['csrf_token'] ?? 'MISSING'));
    error_log("Session CSRF token: " . ($_SESSION['csrf_token'] ?? 'MISSING'));

    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors = ["Invalid form submission - CSRF token validation failed"];
        error_log("CSRF validation failed in create_job.php");
    } else {
        // Validate form data
        $errors = validateJobForm($_POST);
    }

    if (empty($errors)) {
        // Generate job number: job-YYYYMMDD-XXXX
        $job_number = "job-" . date("Ymd") . "-" . str_pad(rand(0,9999), 4, '0', STR_PAD_LEFT);
    
    // Save autocomplete data
    saveForAutocomplete("customer_names", "name", $_POST["customer_name"]);
    saveForAutocomplete("job_titles", "title", $_POST["job_title"]);
    saveForAutocomplete("return_places", "place", $_POST["return_place"]);
    saveForAutocomplete("deposit_places", "place", $_POST["deposit_place"]);
    saveForAutocomplete("depositor_names", "name", $_POST["depositor_name"]);
    
    // Calculate service fee based on container type
    $service_fee = "";
    switch ($_POST["container_type"]) {
        case "เปล่าสั้น":
            $service_fee = "214";
            break;
        case "เปล่ายาว":
            $service_fee = "321";
            break;
        case "หนักสั้น":
            $service_fee = "535";
            break;
        case "หนักยาว":
            $service_fee = "1070";
            break;
        default:
            $service_fee = "0";
    }
    
    // ตรวจสอบค่าวันที่ว่างเปล่า
    $first_return_date = !empty($_POST["first_return_date"]) ? $_POST["first_return_date"] : null;
    $closing_date = !empty($_POST["closing_date"]) ? $_POST["closing_date"] : null;
    $closing_time = !empty($_POST["closing_time"]) ? $_POST["closing_time"] : null;
    $deposit_date = !empty($_POST["deposit_date"]) ? $_POST["deposit_date"] : null;

    $stmt = $pdo->prepare("INSERT INTO jobs (
        job_number, user_id, job_date, customer_name, user_name, job_title, 
        depositor_name, booking_bl, container_no, container_size, first_return_date, 
        closing_date, closing_time, return_place, deposit_date, deposit_place, 
        container_type, service_fee
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $stmt->execute([
        $job_number,
        $_SESSION["user_id"],
        $_POST["job_date"] ?: date('Y-m-d'),
        $_POST["customer_name"],
        $_SESSION["username"], // Current user as user_name
        $_POST["job_title"],
        $_POST["depositor_name"],
        $_POST["booking_bl"],
        $_POST["container_no"],
        $_POST["container_size"],
        $first_return_date,
        $closing_date,
        $closing_time,
        $_POST["return_place"],
        $deposit_date,
        $_POST["deposit_place"],
        $_POST["container_type"],
        $service_fee
    ]);

        echo "<div class='alert alert-success'>Job created with number: $job_number</div>";
    } else {
        // Display validation errors
        echo "<div class='alert alert-danger'>";
        echo "<strong>Please fix the following errors:</strong><ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul></div>";
    }
}

// Get autocomplete data
try {
    $customers = $pdo->query("SELECT name FROM customer_names ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
    $jobTitles = $pdo->query("SELECT title FROM job_titles ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
    $returnPlaces = $pdo->query("SELECT place FROM return_places ORDER BY place")->fetchAll(PDO::FETCH_COLUMN);
    $depositPlaces = $pdo->query("SELECT place FROM deposit_places ORDER BY place")->fetchAll(PDO::FETCH_COLUMN);
    $depositorNames = $pdo->query("SELECT name FROM depositor_names ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error loading autocomplete data: " . $e->getMessage() . "</div>";
    $customers = $jobTitles = $returnPlaces = $depositPlaces = $depositorNames = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Create Job - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">Container System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
                </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav ms-auto">
                <?php if (isset($_SESSION["username"])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </li>
                <?php else: ?>
                <li class="nav-item">
                    <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block">
            <div class="sidebar p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="job_list.php">
                            <i class="fas fa-list"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="create_job.php">
                            <i class="fas fa-plus"></i> Create Job
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user_list.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <div class="col-md-9 col-lg-10">
            <div class="content">
                <h2>Create Job</h2>
                <p>ทุกรายการที่บันทึก ทำการเลือกก่อน ไม่มีข้อมูลค่อยพิมพ์บันทึกเพิ่ม</p>
                
                <div class="card">
                    <div class="card-body">
                        <form method="post" class="needs-validation" novalidate id="jobForm">
                            <?= generateCSRFToken() ?>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label>วันที่สร้างงาน</label>
                                    <input type="date" name="job_date" class="form-control" value="<?= date('Y-m-d') ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label>ชื่อลูกค้า <span class="text-danger">*</span></label>
                                    <input type="text" name="customer_name" class="form-control" list="customer_list" required
                                           value="<?= htmlspecialchars($_POST['customer_name'] ?? '') ?>">
                                    <datalist id="customer_list">
                                        <?php foreach($customers as $customer): ?>
                                        <option value="<?= htmlspecialchars($customer) ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                    <div class="invalid-feedback">
                                        Please provide a customer name.
                                    </div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label>ชื่องาน <span class="text-danger">*</span></label>
                                    <input type="text" name="job_title" class="form-control" list="job_title_list" required
                                           value="<?= htmlspecialchars($_POST['job_title'] ?? '') ?>">
                                    <datalist id="job_title_list">
                                        <?php foreach($jobTitles as $title): ?>
                                        <option value="<?= htmlspecialchars($title) ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                    <div class="invalid-feedback">
                                        Please provide a job title.
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Rest of the form fields -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>ชื่อคนฝากตู้</label>
                                    <input type="text" name="depositor_name" class="form-control" list="depositor_list"
                                           value="<?= htmlspecialchars($_POST['depositor_name'] ?? '') ?>">
                                        <div class="form-text">(พิมพ์เพิ่ม ชื่อ วรรค นามสกุล)</div>
                                    <datalist id="depositor_list">
                                        <?php foreach($depositorNames as $depositor): ?>
                                        <option value="<?= htmlspecialchars($depositor) ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label>Booking/BL</label>
                                    <input type="text" name="booking_bl" class="form-control">
                                </div>
                            </div>
                            
                            <!-- More form fields... -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>Container no. <span class="text-danger">*</span></label>
                                    <input type="text" name="container_no" maxlength="11" pattern="[A-Z]{4}[0-9]{7}"
                                        class="form-control" required placeholder="ABCD1234567" style="text-transform: uppercase;"
                                        value="<?= htmlspecialchars($_POST['container_no'] ?? '') ?>">
                                    <div class="form-text">เลขตู้ Container (4 ตัวอักษร + 7 ตัวเลข)</div>
                                    <div class="invalid-feedback">
                                        Please provide a valid container number (4 letters + 7 numbers).
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label>Size ตู้ <span class="text-danger">*</span></label>
                                    <select name="container_size" class="form-control" required>
                                        <option value="">เลือกขนาดตู้</option>
                                        <option value="20" <?= ($_POST['container_size'] ?? '') === '20' ? 'selected' : '' ?>>20 ฟุต</option>
                                        <option value="40" <?= ($_POST['container_size'] ?? '') === '40' ? 'selected' : '' ?>>40 ฟุต</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a container size.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>First Return date</label>
                                    <input type="date" name="first_return_date" class="form-control">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label>สถานที่คืนตู้ <span class="text-danger">*</span></label>
                                    <input type="text" name="return_place" class="form-control" list="return_place_list" required
                                           value="<?= htmlspecialchars($_POST['return_place'] ?? '') ?>">
                                    <datalist id="return_place_list">
                                        <?php foreach($returnPlaces as $place): ?>
                                        <option value="<?= htmlspecialchars($place) ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                    <div class="invalid-feedback">
                                        Please provide a return place.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>Closing date</label>
                                    <input type="date" name="closing_date" class="form-control">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label>Closing time</label>
                                    <input type="time" name="closing_time" class="form-control">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>วันที่ฝากตู้</label>
                                    <input type="date" name="deposit_date" class="form-control">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label>สถานที่ฝากตู้</label>
                                    <input type="text" name="deposit_place" class="form-control" list="deposit_place_list">
                                    <datalist id="deposit_place_list">
                                        <?php foreach($depositPlaces as $place): ?>
                                        <option value="<?= htmlspecialchars($place) ?>">
                                        <?php endforeach; ?>
                                    </datalist>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label>ประเภทตู้ <span class="text-danger">*</span></label>
                                    <select name="container_type" id="container_type" class="form-control" required onchange="updateServiceFee()">
                                        <option value="">เลือกประเภทตู้</option>
                                        <option value="เปล่าสั้น" <?= ($_POST['container_type'] ?? '') === 'เปล่าสั้น' ? 'selected' : '' ?>>เปล่าสั้น</option>
                                        <option value="เปล่ายาว" <?= ($_POST['container_type'] ?? '') === 'เปล่ายาว' ? 'selected' : '' ?>>เปล่ายาว</option>
                                        <option value="หนักสั้น" <?= ($_POST['container_type'] ?? '') === 'หนักสั้น' ? 'selected' : '' ?>>หนักสั้น</option>
                                        <option value="หนักยาว" <?= ($_POST['container_type'] ?? '') === 'หนักยาว' ? 'selected' : '' ?>>หนักยาว</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a container type.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label>ค่าบริการ</label>
                                    <input type="text" id="service_fee" class="form-control" readonly value="เปล่า 350">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">สร้างงาน</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function updateServiceFee() {
    const containerType = document.getElementById('container_type').value;
    const serviceFee = document.getElementById('service_fee');

    if (containerType === 'เปล่าสั้น') {
        serviceFee.value = '214';
    } else if (containerType === 'เปล่ายาว') {
        serviceFee.value = '321';
    } else if (containerType === 'หนักสั้น') {
        serviceFee.value = '353';
    } else if (containerType === 'หนักยาว') {
        serviceFee.value = '1070';
    } else {
        serviceFee.value = '';
    }
}

// Form validation
(function() {
    'use strict';

    // Container number validation
    const containerNoInput = document.querySelector('input[name="container_no"]');
    if (containerNoInput) {
        containerNoInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
            const pattern = /^[A-Z]{4}[0-9]{7}$/;
            if (this.value && !pattern.test(this.value)) {
                this.setCustomValidity('Container number must be 4 letters followed by 7 numbers');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // Form submission validation
    const form = document.getElementById('jobForm');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }

    // Initialize service fee on page load
    updateServiceFee();
})();
</script>
</body>
</html>
<?php include '../includes/footer.php'; ?>
