<?php
// Production mode - disable error display
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php'; // ไฟล์่ js 
require_once '../includes/auth.php'; // ยืนยันตัวตน

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

// ตรวจสอบการล็อกอินด้วยฟังก์ชันจาก auth.php
requireLogin();

$where = [];
$params = [];

// Filter by status
if (!empty($_GET["status"])) {
    $where[] = "status = ?";
    $params[] = $_GET["status"];
}

// Filter by user
if (!empty($_GET["user"])) {
    $where[] = "user_id = ?";
    $params[] = $_GET["user"];
}

// Filter by date range
if (!empty($_GET["start_date"])) {
    $where[] = "job_date >= ?";
    $params[] = $_GET["start_date"];
}
if (!empty($_GET["end_date"])) {
    $where[] = "job_date <= ?";
    $params[] = $_GET["end_date"];
}

// Get users for dropdown
$users = $pdo->query("SELECT id, username FROM users ORDER BY username")->fetchAll();

// Build query
$sql = "SELECT jobs.*, users.username FROM jobs JOIN users ON users.id = jobs.user_id";
if ($where) $sql .= " WHERE " . implode(" AND ", $where);
$sql .= " ORDER BY job_date DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Reports - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">Container System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
                </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav ms-auto">
                <?php if (isset($_SESSION["username"])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-id-card"></i> Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block">
            <div class="sidebar p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="job_list.php">
                            <i class="fas fa-list"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create_job.php">
                            <i class="fas fa-plus"></i> Create Job
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="report.php">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user_list.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <div class="col-md-9 col-lg-10">
            <div class="content">
                <h2>Report</h2>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-filter"></i> Filter Reports
                    </div>
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label>Status</label>
                                <select name="status" class="form-select">
                                    <option value="">All</option>
                                    <option value="Pending" <?= isset($_GET['status']) && $_GET['status'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="Done" <?= isset($_GET['status']) && $_GET['status'] == 'Done' ? 'selected' : '' ?>>Done</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>User</label>
                                <select name="user" class="form-select">
                                    <option value="">All</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?= $user['id'] ?>" <?= isset($_GET['user']) && $_GET['user'] == $user['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($user['username']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>Start Date</label>
                                <input type="date" name="start_date" class="form-control" value="<?= htmlspecialchars($_GET['start_date'] ?? '') ?>">
                            </div>
                            <div class="col-md-3">
                                <label>End Date</label>
                                <input type="date" name="end_date" class="form-control" value="<?= htmlspecialchars($_GET['end_date'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label>&nbsp;</label><br>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="export.php?type=csv&<?= http_build_query($_GET) ?>" class="btn btn-success">
                                    <i class="fas fa-file-csv"></i> Export to CSV
                                </a>
                                <!-- <a href="export.php?type=pdf&<?//= http_build_query($_GET) ?>" class="btn btn-danger">
                                    <i class="fas fa-file-pdf"></i> Export to PDF
                                </a> -->
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-table"></i> Report Results
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr><th>Customer</th>
                                    <th>Job Title</th>
                                    <th>Booking/BL</th>
                                    <th>Container No.</th>
                                    <th>Depositor Name</th>
                                    <th>Size</th>
                                    <th>Container Type</th>
                                    <th>Job Date</th>
                                    <th>First Return</th>
                                    <th>Closing Date</th>
                                    <th>Return Place</th>
                                    <th>Deposit Date</th>
                                    <th>Deposit Place</th>
                                    <th>Final Return</th>
                                    <th>Return User</th>
                                    <th>Status</th>
                                    <th>Service Fee</th>
                                    <th>User</th>
                                    <th>Created At</th>  
                                </tr>
                                <?php foreach ($stmt as $row): ?>
                                <tr>
                                    <td><?= htmlspecialchars($row["customer_name"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["job_title"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["booking_bl"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["container_no"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["depositor_name"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["container_size"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["container_type"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["job_date"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["first_return_date"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["closing_date"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["return_place"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["deposit_date"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["deposit_place"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["final_return_date"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["return_user"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["status"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["service_fee"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["username"] ?? '') ?></td>
                                    <td><?= htmlspecialchars($row["created_at"] ?? '') ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
