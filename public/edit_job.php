<?php
// แสดงข้อผิดพลาด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
if (!isset($pdo)) {
    die("Database connection failed");
}

// ตรวจสอบการล็อกอิน
requireLogin();

// ฟังก์ชันสำหรับบันทึกข้อมูลสำหรับ autocomplete
function saveForAutocomplete($table, $field, $value) {
    global $pdo;
    if (empty($value)) return;
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO {$table} ({$field}) VALUES (?)");
    $stmt->execute([$value]);
}

// ฟังก์ชันสำหรับบันทึกประวัติการแก้ไข
function logEditHistory($job_id, $field_name, $old_value, $new_value) {
    global $pdo;
    
    // ถ้าค่าเดิมและค่าใหม่เหมือนกัน ไม่ต้องบันทึก
    if ($old_value === $new_value) return;
    
    $stmt = $pdo->prepare("INSERT INTO job_edit_history 
        (job_id, user_id, username, field_name, old_value, new_value, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->execute([
        $job_id,
        $_SESSION["user_id"],
        $_SESSION["username"],
        $field_name,
        $old_value,
        $new_value,
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
}

// ตรวจสอบว่ามี job ID หรือไม่
if (!isset($_GET["id"])) {
    echo "<div class='alert alert-danger'>No job ID provided</div>";
    exit;
}

$job_id = $_GET["id"];

// ดึงข้อมูล job ปัจจุบัน
$stmt = $pdo->prepare("SELECT * FROM jobs WHERE id = ?");
$stmt->execute([$job_id]);
$job = $stmt->fetch();

if (!$job) {
    echo "<div class='alert alert-danger'>Job not found</div>";
    exit;
}

// ตรวจสอบสิทธิ์การแก้ไข (admin สามารถแก้ไขได้ทุก job, user แก้ไขได้เฉพาะของตัวเอง)
if ($_SESSION["role"] !== 'admin' && $job["user_id"] != $_SESSION["user_id"]) {
    echo "<div class='alert alert-danger'>You do not have permission to edit this job. You can only edit jobs that you created.</div>";
    echo "<a href='job_list.php' class='btn btn-secondary'>Back to Job List</a>";
    exit;
}

// ประมวลผลการส่งฟอร์ม
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // บันทึกข้อมูลสำหรับ autocomplete
    saveForAutocomplete("customer_names", "name", $_POST["customer_name"]);
    saveForAutocomplete("job_titles", "title", $_POST["job_title"]);
    saveForAutocomplete("return_places", "place", $_POST["return_place"]);
    saveForAutocomplete("deposit_places", "place", $_POST["deposit_place"]);
    saveForAutocomplete("depositor_names", "name", $_POST["depositor_name"]);
    saveForAutocomplete("return_users", "name", $_POST["return_user"]);
    
    // คำนวณค่าบริการตามประเภทตู้
    $service_fee = "";
    switch ($_POST["container_type"]) {
        case "เปล่าสั้น":
            $service_fee = "214";
            break;
        case "เปล่ายาว":
            $service_fee = "321";
            break;
        case "หนักสั้น":
            $service_fee = "535";
            break;
        case "หนักยาว":
            $service_fee = "1070";
            break;
        default:
            $service_fee = "0";
    }
    
    // ตรวจสอบค่าวันที่ว่างเปล่า
    $first_return_date = !empty($_POST["first_return_date"]) ? $_POST["first_return_date"] : null;
    $closing_date = !empty($_POST["closing_date"]) ? $_POST["closing_date"] : null;
    $closing_time = !empty($_POST["closing_time"]) ? $_POST["closing_time"] : null;
    $deposit_date = !empty($_POST["deposit_date"]) ? $_POST["deposit_date"] : null;
    $final_return_date = !empty($_POST["final_return_date"]) ? $_POST["final_return_date"] : null;
    
    // เปรียบเทียบและบันทึกประวัติการแก้ไข
    $fields_to_check = [
        'job_date' => $_POST["job_date"] ?: date('Y-m-d'),
        'customer_name' => $_POST["customer_name"],
        'job_title' => $_POST["job_title"],
        'depositor_name' => $_POST["depositor_name"],
        'booking_bl' => $_POST["booking_bl"],
        'container_no' => $_POST["container_no"],
        'container_size' => $_POST["container_size"],
        'first_return_date' => $first_return_date,
        'closing_date' => $closing_date,
        'closing_time' => $closing_time,
        'return_place' => $_POST["return_place"],
        'deposit_date' => $deposit_date,
        'deposit_place' => $_POST["deposit_place"],
        'container_type' => $_POST["container_type"],
        'service_fee' => $service_fee,
        'final_return_date' => $final_return_date,
        'return_user' => $_POST["return_user"],
        'status' => $_POST["status"],
        'remark' => $_POST["remark"]
    ];
    
    foreach ($fields_to_check as $field => $new_value) {
        logEditHistory($job_id, $field, $job[$field], $new_value);
    }
    
    // อัปเดตข้อมูล job
    $stmt = $pdo->prepare("UPDATE jobs SET 
        job_date = ?, customer_name = ?, job_title = ?, depositor_name = ?, 
        booking_bl = ?, container_no = ?, container_size = ?, first_return_date = ?, 
        closing_date = ?, closing_time = ?, return_place = ?, deposit_date = ?, 
        deposit_place = ?, container_type = ?, service_fee = ?, final_return_date = ?, 
        return_user = ?, status = ?, remark = ?
        WHERE id = ?");
    
    $stmt->execute([
        $_POST["job_date"] ?: date('Y-m-d'),
        $_POST["customer_name"],
        $_POST["job_title"],
        $_POST["depositor_name"],
        $_POST["booking_bl"],
        $_POST["container_no"],
        $_POST["container_size"],
        $first_return_date,
        $closing_date,
        $closing_time,
        $_POST["return_place"],
        $deposit_date,
        $_POST["deposit_place"],
        $_POST["container_type"],
        $service_fee,
        $final_return_date,
        $_POST["return_user"],
        $_POST["status"],
        $_POST["remark"],
        $job_id
    ]);
    
    echo "<div class='alert alert-success'>Job updated successfully!</div>";
    
    // รีเฟรชข้อมูล job หลังจากอัปเดต
    $stmt = $pdo->prepare("SELECT * FROM jobs WHERE id = ?");
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
}

// ดึงข้อมูลสำหรับ autocomplete
try {
    $customers = $pdo->query("SELECT name FROM customer_names ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
    $jobTitles = $pdo->query("SELECT title FROM job_titles ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
    $returnPlaces = $pdo->query("SELECT place FROM return_places ORDER BY place")->fetchAll(PDO::FETCH_COLUMN);
    $depositPlaces = $pdo->query("SELECT place FROM deposit_places ORDER BY place")->fetchAll(PDO::FETCH_COLUMN);
    $depositorNames = $pdo->query("SELECT name FROM depositor_names ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
    $returnUsers = $pdo->query("SELECT name FROM return_users ORDER BY name")->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error loading autocomplete data: " . $e->getMessage() . "</div>";
    $customers = $jobTitles = $returnPlaces = $depositPlaces = $depositorNames = $returnUsers = [];
}

// ดึงประวัติการแก้ไข
$stmt = $pdo->prepare("SELECT jeh.*, u.username as editor_username 
    FROM job_edit_history jeh 
    JOIN users u ON u.id = jeh.user_id 
    WHERE jeh.job_id = ? 
    ORDER BY jeh.edit_timestamp DESC 
    LIMIT 50");
$stmt->execute([$job_id]);
$edit_history = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Edit Job - Container System</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link i {
            margin-right: 10px;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .edit-history {
            max-height: 400px;
            overflow-y: auto;
        }
        .history-item {
            border-left: 3px solid #007bff;
            padding-left: 10px;
            margin-bottom: 10px;
        }
        .history-item.warning {
            border-left-color: #ffc107;
        }
        .history-item.danger {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="dashboard.php">Container System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="job_list.php"><i class="fas fa-list"></i> Jobs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="create_job.php"><i class="fas fa-plus"></i> Create Job</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="report.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                <li class="nav-item">
                    <a class="nav-link" href="user_list.php"><i class="fas fa-users"></i> Users</a>
                </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav ms-auto">
                <?php if (isset($_SESSION["username"])): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($_SESSION["username"]); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-circle"></i> Profile</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                    </ul>
                </li>
                <?php else: ?>
                <li class="nav-item">
                    <a class="nav-link" href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 col-lg-2 d-md-block">
            <div class="sidebar p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="job_list.php">
                            <i class="fas fa-list"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="create_job.php">
                            <i class="fas fa-plus"></i> Create Job
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="report.php">
                            <i class="fas fa-chart-bar"></i> Reports
                        </a>
                    </li>
                    <?php if (isset($_SESSION["role"]) && $_SESSION["role"] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="user_list.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <div class="col-md-9 col-lg-10">
            <div class="content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Edit Job: <?= htmlspecialchars($job["job_number"]) ?></h2>
                    <div>
                        <a href="view_job.php?id=<?= $job_id ?>" class="btn btn-info">
                            <i class="fas fa-eye"></i> View Job
                        </a>
                        <a href="job_list.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>

                <!-- แสดงข้อมูลผู้แก้ไขล่าสุด -->
                <?php if (!empty($edit_history)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    แก้ไขล่าสุดโดย: <strong><?= htmlspecialchars($edit_history[0]['editor_username']) ?></strong>
                    เมื่อ: <?= date('d/m/Y H:i:s', strtotime($edit_history[0]['edit_timestamp'])) ?>
                </div>
                <?php endif; ?>

                <div class="row">
                    <!-- ฟอร์มแก้ไข -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-edit"></i> Edit Job Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label>วันที่สร้างงาน</label>
                                            <input type="date" name="job_date" class="form-control"
                                                   value="<?= htmlspecialchars($job['job_date']) ?>">
                                        </div>

                                        <div class="col-md-4 mb-3">
                                            <label>ชื่อลูกค้า</label>
                                            <input type="text" name="customer_name" class="form-control"
                                                   list="customer_list" required
                                                   value="<?= htmlspecialchars($job['customer_name']) ?>">
                                            <datalist id="customer_list">
                                                <?php foreach($customers as $customer): ?>
                                                <option value="<?= htmlspecialchars($customer) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>

                                        <div class="col-md-4 mb-3">
                                            <label>ชื่องาน</label>
                                            <input type="text" name="job_title" class="form-control"
                                                   list="job_title_list" required
                                                   value="<?= htmlspecialchars($job['job_title']) ?>">
                                            <datalist id="job_title_list">
                                                <?php foreach($jobTitles as $title): ?>
                                                <option value="<?= htmlspecialchars($title) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>ชื่อคนฝากตู้</label>
                                            <input type="text" name="depositor_name" class="form-control"
                                                   list="depositor_list"
                                                   value="<?= htmlspecialchars((string)($job['depositor_name'] ?? '')) ?>">
                                                <div class="form-text">(พิมพ์เพิ่ม ชื่อ วรรค นามสกุล)</div>
                                            <datalist id="depositor_list">
                                                <?php foreach($depositorNames as $name): ?>
                                                <option value="<?= htmlspecialchars($name) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>Booking/BL</label>
                                            <input type="text" name="booking_bl" class="form-control"
                                                   value="<?= htmlspecialchars($job['booking_bl']) ?>">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>Container no.</label>
                                            <input type="text" name="container_no" maxlength="11"
                                                   pattern="[A-Z]{4}[0-9]{7}" class="form-control" required
                                                   placeholder="ABCD1234567"
                                                   value="<?= htmlspecialchars($job['container_no']) ?>">
                                            <div class="form-text">เลขตู้ Container (4 ตัวอักษร + 7 ตัวเลข)</div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>Size ตู้</label>
                                            <select name="container_size" class="form-control" required>
                                                <option value="20" <?= $job['container_size'] == '20' ? 'selected' : '' ?>>20 ฟุต</option>
                                                <option value="40" <?= $job['container_size'] == '40' ? 'selected' : '' ?>>40 ฟุต</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>First Return date</label>
                                            <input type="date" name="first_return_date" class="form-control"
                                                   value="<?= htmlspecialchars($job['first_return_date']) ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>สถานที่คืนตู้</label>
                                            <input type="text" name="return_place" class="form-control"
                                                   list="return_place_list" required
                                                   value="<?= htmlspecialchars($job['return_place']) ?>">
                                            <datalist id="return_place_list">
                                                <?php foreach($returnPlaces as $place): ?>
                                                <option value="<?= htmlspecialchars($place) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>Closing date</label>
                                            <input type="date" name="closing_date" class="form-control"
                                                   value="<?= htmlspecialchars($job['closing_date']) ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>Closing time</label>
                                            <input type="time" name="closing_time" class="form-control"
                                                   value="<?= htmlspecialchars($job['closing_time']) ?>">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>วันที่ฝากตู้</label>
                                            <input type="date" name="deposit_date" class="form-control"
                                                   value="<?= htmlspecialchars($job['deposit_date']) ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>สถานที่ฝากตู้</label>
                                            <input type="text" name="deposit_place" class="form-control"
                                                   list="deposit_place_list"
                                                   value="<?= htmlspecialchars($job['deposit_place']) ?>">
                                            <datalist id="deposit_place_list">
                                                <?php foreach($depositPlaces as $place): ?>
                                                <option value="<?= htmlspecialchars($place) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>ประเภทตู้</label>
                                            <select name="container_type" id="container_type" class="form-control" required onchange="updateServiceFee()">
                                                <option value="เปล่าสั้น" <?= $job['container_type'] == 'เปล่าสั้น' ? 'selected' : '' ?>>เปล่าสั้น</option>
                                                <option value="เปล่ายาว" <?= $job['container_type'] == 'เปล่ายาว' ? 'selected' : '' ?>>เปล่ายาว</option>
                                                <option value="หนักสั้น" <?= $job['container_type'] == 'หนักสั้น' ? 'selected' : '' ?>>หนักสั้น</option>
                                                <option value="หนักยาว" <?= $job['container_type'] == 'หนักยาว' ? 'selected' : '' ?>>หนักยาว</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>ค่าบริการ</label>
                                            <input type="text" id="service_fee" class="form-control" readonly
                                                   value="<?= htmlspecialchars($job['service_fee']) ?>">
                                        </div>
                                    </div>

                                    <!-- ส่วนข้อมูลการคืนตู้ -->
                                    <hr>
                                    <h6><i class="fas fa-undo"></i> ข้อมูลการคืนตู้</h6>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>วันที่คืนตู้จริง</label>
                                            <input type="date" name="final_return_date" class="form-control"
                                                   value="<?= htmlspecialchars($job['final_return_date']) ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label>ชื่อผู้คืนตู้</label>
                                            <input type="text" name="return_user" class="form-control"
                                                   list="return_users_list"
                                                   value="<?= htmlspecialchars((string)($job['return_user'] ?? '')) ?>">
                                            <datalist id="return_users_list">
                                                <?php foreach($returnUsers as $user): ?>
                                                <option value="<?= htmlspecialchars($user) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label>สถานะ</label>
                                            <select name="status" class="form-control" required>
                                                <option value="Pending" <?= $job['status'] == 'Pending' ? 'selected' : '' ?>>Pending</option>
                                                <option value="Done" <?= $job['status'] == 'Done' ? 'selected' : '' ?>>Done</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label>Remark</label>
                                        <textarea name="remark" class="form-control" rows="3"><?= htmlspecialchars($job['remark'] ?? "") ?></textarea>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> บันทึกการแก้ไข
                                        </button>
                                        <a href="view_job.php?id=<?= $job_id ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> ยกเลิก
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- ประวัติการแก้ไข -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-history"></i> ประวัติการแก้ไข</h5>
                            </div>
                            <div class="card-body">
                                <div class="edit-history">
                                    <?php if (empty($edit_history)): ?>
                                        <p class="text-muted">ยังไม่มีประวัติการแก้ไข</p>
                                    <?php else: ?>
                                        <?php foreach ($edit_history as $history): ?>
                                            <div class="history-item">
                                                <small class="text-muted">
                                                    <?= date('d/m/Y H:i:s', strtotime($history['edit_timestamp'])) ?>
                                                </small>
                                                <div>
                                                    <strong><?= htmlspecialchars($history['editor_username']) ?></strong>
                                                    แก้ไข <em><?= htmlspecialchars($history['field_name']) ?></em>
                                                </div>
                                                <?php if ($history['old_value'] !== $history['new_value']): ?>
                                                    <div class="small">
                                                        <span class="text-danger">เดิม:</span>
                                                        <?= htmlspecialchars($history['old_value'] ?: '(ว่าง)') ?><br>
                                                        <span class="text-success">ใหม่:</span>
                                                        <?= htmlspecialchars($history['new_value'] ?: '(ว่าง)') ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- สถิติการแก้ไข -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-chart-pie"></i> สถิติการแก้ไข</h6>
                            </div>
                            <div class="card-body">
                                <?php
                                $edit_count = count($edit_history);
                                $unique_editors = array_unique(array_column($edit_history, 'editor_username'));
                                $editor_count = count($unique_editors);
                                ?>
                                <p><strong>จำนวนการแก้ไข:</strong> <?= $edit_count ?> ครั้ง</p>
                                <p><strong>จำนวนผู้แก้ไข:</strong> <?= $editor_count ?> คน</p>
                                <?php if (!empty($unique_editors)): ?>
                                    <p><strong>ผู้แก้ไข:</strong></p>
                                    <ul class="list-unstyled">
                                        <?php foreach ($unique_editors as $editor): ?>
                                            <li><i class="fas fa-user"></i> <?= htmlspecialchars($editor) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
function updateServiceFee() {
    const containerType = document.getElementById('container_type').value;
    const serviceFee = document.getElementById('service_fee');

    if (containerType === 'เปล่าสั้น') {
        serviceFee.value = '214';
    } else if (containerType === 'เปล่ายาว') {
        serviceFee.value = '321';
    } else if (containerType === 'หนักสั้น') {
        serviceFee.value = '353';
    } else if (containerType === 'หนักยาว') {
        serviceFee.value = '1070';
    } else {
        serviceFee.value = '';
    }
}

// เตือนผู้ใช้เมื่อมีการเปลี่ยนแปลงข้อมูล
let originalFormData = new FormData(document.querySelector('form'));
let hasChanges = false;

document.querySelector('form').addEventListener('input', function() {
    hasChanges = true;
});

window.addEventListener('beforeunload', function(e) {
    if (hasChanges) {
        e.preventDefault();
        e.returnValue = 'คุณมีการเปลี่ยนแปลงข้อมูลที่ยังไม่ได้บันทึก ต้องการออกจากหน้านี้หรือไม่?';
    }
});

document.querySelector('form').addEventListener('submit', function() {
    hasChanges = false;
});

// Auto-save draft (optional feature)
function saveDraft() {
    const formData = new FormData(document.querySelector('form'));
    const draftData = {};
    for (let [key, value] of formData.entries()) {
        draftData[key] = value;
    }
    localStorage.setItem('job_edit_draft_<?= $job_id ?>', JSON.stringify(draftData));
}

// Save draft every 30 seconds
setInterval(saveDraft, 30000);

// Load draft on page load
window.addEventListener('load', function() {
    const draftData = localStorage.getItem('job_edit_draft_<?= $job_id ?>');
    if (draftData) {
        const data = JSON.parse(draftData);
        const form = document.querySelector('form');

        // Show notification about draft
        const draftAlert = document.createElement('div');
        draftAlert.className = 'alert alert-warning alert-dismissible fade show';
        draftAlert.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            พบข้อมูลร่างที่บันทึกไว้
            <button type="button" class="btn btn-sm btn-outline-dark ms-2" onclick="loadDraft()">โหลดข้อมูลร่าง</button>
            <button type="button" class="btn btn-sm btn-outline-danger ms-1" onclick="clearDraft()">ลบข้อมูลร่าง</button>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        form.parentNode.insertBefore(draftAlert, form);
    }
});

function loadDraft() {
    const draftData = localStorage.getItem('job_edit_draft_<?= $job_id ?>');
    if (draftData) {
        const data = JSON.parse(draftData);
        const form = document.querySelector('form');

        for (let [key, value] of Object.entries(data)) {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = value;
                if (key === 'container_type') {
                    updateServiceFee();
                }
            }
        }
        hasChanges = true;
    }
}

function clearDraft() {
    localStorage.removeItem('job_edit_draft_<?= $job_id ?>');
    location.reload();
}

// Clear draft after successful save
<?php if ($_SERVER["REQUEST_METHOD"] == "POST" && !isset($error)): ?>
localStorage.removeItem('job_edit_draft_<?= $job_id ?>');
<?php endif; ?>
</script>
</body>
</html>
<?php include '../includes/footer.php'; ?>
