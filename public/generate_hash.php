<?php
// Script สำหรับสร้าง password hash
echo "<h1>🔐 Password Hash Generator</h1>";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $password = $_POST['password'];
    
    if (!empty($password)) {
        $hash = password_hash($password, PASSWORD_DEFAULT);
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3 style='color: #155724; margin: 0;'>✅ Hash Generated</h3>";
        echo "<p style='color: #155724; margin: 5px 0;'><strong>Password:</strong> " . htmlspecialchars($password) . "</p>";
        echo "<p style='color: #155724; margin: 5px 0; word-break: break-all;'><strong>Hash:</strong> " . $hash . "</p>";
        echo "</div>";
        
        echo "<h3>SQL Command:</h3>";
        echo "<textarea readonly style='width: 100%; height: 80px; font-family: monospace;'>";
        echo "UPDATE users SET password_hash = '" . $hash . "' WHERE username = 'admin';";
        echo "</textarea>";
        
        // ทดสอบ hash
        if (password_verify($password, $hash)) {
            echo "<p style='color: green;'>✅ Hash verification: SUCCESS</p>";
        } else {
            echo "<p style='color: red;'>❌ Hash verification: FAILED</p>";
        }
    }
} else {
    echo "<form method='post' style='max-width: 400px; margin: 20px 0;'>";
    echo "<div style='margin: 10px 0;'>";
    echo "<label>Enter Password:</label><br>";
    echo "<input type='text' name='password' required style='width: 100%; padding: 8px; margin: 5px 0;' placeholder='Enter new password'>";
    echo "</div>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Generate Hash</button>";
    echo "</form>";
    
    echo "<hr>";
    echo "<h3>Current Admin Info:</h3>";
    echo "<p><strong>Username:</strong> admin</p>";
    echo "<p><strong>Password:</strong> admin123</p>";
    echo "<p><strong>Hash:</strong> \$2y\$10\$D9cUKMfr9WW48Xjz1JmH7.tPtQETiHj48SgQQ7bEV6K1eMzt41CCu</p>";
}

echo "<hr>";
echo "<h3>Common Password Hashes:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Password</th><th>Hash</th></tr>";

$common_passwords = [
    'admin123' => '$2y$10$D9cUKMfr9WW48Xjz1JmH7.tPtQETiHj48SgQQ7bEV6K1eMzt41CCu',
    'password' => password_hash('password', PASSWORD_DEFAULT),
    '123456' => password_hash('123456', PASSWORD_DEFAULT),
    'admin' => password_hash('admin', PASSWORD_DEFAULT)
];

foreach ($common_passwords as $pwd => $hash) {
    echo "<tr>";
    echo "<td style='padding: 5px;'>" . $pwd . "</td>";
    echo "<td style='padding: 5px; font-family: monospace; font-size: 12px; word-break: break-all;'>" . $hash . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p style='color: #856404; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin-top: 20px;'>";
echo "⚠️ <strong>คำเตือน:</strong> ลบไฟล์นี้ทิ้งหลังใช้งาน เพื่อความปลอดภัย";
echo "</p>";
?>
