# Container System - Security Configuration

# Disable directory browsing
Options -Indexes

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to PHP configuration files
<Files "*.ini">
    Order allow,deny
    <PERSON>y from all
</Files>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Enable HTTPS redirect (uncomment if using SSL)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# PHP Security Settings
<IfModule mod_php.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# Prevent execution of PHP files in upload directories
<Directory "uploads">
    <Files "*.php">
        Order allow,deny
        Deny from all
    </Files>
</Directory>

# Custom error pages
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html
