<?php
// Script สำหรับเปลี่ยนรหัสผ่าน admin
// ใช้เฉพาะครั้งเดียว แล้วลบทิ้ง

// ตรวจสอบว่าเป็น localhost เท่านั้น (เพื่อความปลอดภัย)
if ($_SERVER['REMOTE_ADDR'] !== '127.0.0.1' && $_SERVER['REMOTE_ADDR'] !== '::1') {
    // Comment บรรทัดนี้ออกถ้าต้องการใช้บน server จริง
    // die("Access denied - localhost only");
}

echo "<h1>🔐 Change Admin Password</h1>";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if ($new_password !== $confirm_password) {
        echo "<p style='color: red;'>รหัสผ่านไม่ตรงกัน!</p>";
    } elseif (strlen($new_password) < 6) {
        echo "<p style='color: red;'>รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร!</p>";
    } else {
        try {
            require_once '../includes/config.php';
            require_once '../includes/db.php';
            
            // สร้าง hash ใหม่
            $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
            
            // อัปเดตรหัสผ่าน admin
            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
            $stmt->execute([$new_hash]);
            
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3 style='color: #155724; margin: 0;'>✅ สำเร็จ!</h3>";
            echo "<p style='color: #155724; margin: 5px 0;'>รหัสผ่าน admin ถูกเปลี่ยนแล้ว</p>";
            echo "<p style='color: #155724; margin: 5px 0;'><strong>Username:</strong> admin</p>";
            echo "<p style='color: #155724; margin: 5px 0;'><strong>New Password:</strong> " . htmlspecialchars($new_password) . "</p>";
            echo "<p style='color: #155724; margin: 5px 0;'><strong>Hash:</strong> " . $new_hash . "</p>";
            echo "</div>";
            
            echo "<p style='color: #856404; background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
            echo "⚠️ <strong>สำคัญ:</strong> ลบไฟล์นี้ทิ้งทันทีหลังใช้งาน เพื่อความปลอดภัย<br>";
            echo "คำสั่งลบ: <code>rm " . __FILE__ . "</code>";
            echo "</p>";
            
            echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้า Login</a></p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
        }
    }
} else {
    // แสดงฟอร์ม
    echo "<form method='post' style='max-width: 400px; margin: 20px 0;'>";
    echo "<div style='margin: 10px 0;'>";
    echo "<label>รหัสผ่านใหม่:</label><br>";
    echo "<input type='password' name='new_password' required style='width: 100%; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    echo "<div style='margin: 10px 0;'>";
    echo "<label>ยืนยันรหัสผ่าน:</label><br>";
    echo "<input type='password' name='confirm_password' required style='width: 100%; padding: 8px; margin: 5px 0;'>";
    echo "</div>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>เปลี่ยนรหัสผ่าน</button>";
    echo "</form>";
    
    echo "<hr>";
    echo "<h3>ข้อมูลปัจจุบัน:</h3>";
    echo "<p><strong>Username:</strong> admin</p>";
    echo "<p><strong>Current Password:</strong> admin123</p>";
    echo "<p><strong>Current Hash:</strong> \$2y\$10\$D9cUKMfr9WW48Xjz1JmH7.tPtQETiHj48SgQQ7bEV6K1eMzt41CCu</p>";
}
?>
