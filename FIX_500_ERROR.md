# 🚨 แก้ไข 500 Internal Server Error

## ปัญหา
เว็บไซต์ขึ้น "500 Internal Server Error" แทน 403

## สาเหตุ
ไฟล์ .htaccess มีปัญหา หรือ server ไม่รองรับ directive บางตัว

## วิธีแก้ไขทันที

### ขั้นตอนที่ 1: ทดสอบไฟล์ HTML ก่อน
```
https://nkslgroup.com/container_system/public/basic_test.html
```
ถ้าทำงาน = server ปกติ, ปัญหาอยู่ที่ .htaccess หรือ PHP

### ขั้นตอนที่ 2: ปิด .htaccess ชั่วคราว
```bash
# เปลี่ยนชื่อ .htaccess ใน root directory
mv .htaccess .htaccess_disabled

# เปลี่ยนชื่อ .htaccess ใน public directory  
mv public/.htaccess public/.htaccess_disabled
```

### ขั้นตอนที่ 3: ทดสอบหลังปิด .htaccess
```
https://nkslgroup.com/container_system/public/simple_test.php
```

### ขั้นตอนที่ 4: ถ้าทำงาน ให้ใช้ .htaccess ที่ปลอดภัย
```bash
# ใช้ .htaccess ที่ปลอดภัยสำหรับ root
cp .htaccess_safe .htaccess

# ใช้ .htaccess ที่ปลอดภัยสำหรับ public
cp public/.htaccess_safe public/.htaccess
```

## คำสั่งแก้ไขแบบ Manual

### ผ่าน SSH:
```bash
cd /var/www/vhosts/nkslgroup.com/httpdocs/container_system/

# ปิด .htaccess ทั้งหมด
mv .htaccess .htaccess_disabled 2>/dev/null || true
mv public/.htaccess public/.htaccess_disabled 2>/dev/null || true

# ทดสอบ
curl -I https://nkslgroup.com/container_system/public/basic_test.html

# ถ้าทำงาน ให้ใช้ .htaccess ที่ปลอดภัย
echo "Options -Indexes" > public/.htaccess
echo "DirectoryIndex public/login.php" > .htaccess
```

### ผ่าน File Manager:
1. เข้า File Manager
2. ไปที่โฟลเดอร์ `container_system`
3. เปลี่ยนชื่อ `.htaccess` เป็น `.htaccess_disabled`
4. ไปที่โฟลเดอร์ `public`
5. เปลี่ยนชื่อ `.htaccess` เป็น `.htaccess_disabled`
6. ทดสอบเว็บไซต์

## การทดสอบทีละขั้นตอน

### 1. ทดสอบ HTML (ไม่ต้องใช้ PHP)
```
https://nkslgroup.com/container_system/public/basic_test.html
```

### 2. ทดสอบ PHP (ไม่ต้องใช้ database)
```
https://nkslgroup.com/container_system/public/simple_test.php
```

### 3. ทดสอบ PHP + Database
```
https://nkslgroup.com/container_system/public/test.php
```

### 4. ทดสอบ Login Page
```
https://nkslgroup.com/container_system/public/login.php
```

## ถ้ายังไม่ได้

### ตรวจสอบ Error Log
```bash
# ดู error log
tail -f /var/log/apache2/error.log
# หรือ
tail -f /var/log/httpd/error_log
```

### ตรวจสอบ PHP Error
สร้างไฟล์ `public/phpinfo.php`:
```php
<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
phpinfo();
?>
```

### ตรวจสอบ PHP Version
```bash
php -v
```

## ไฟล์ที่สร้างใหม่

1. `.htaccess_safe` - ไฟล์ .htaccess ที่ปลอดภัยสำหรับ root
2. `public/.htaccess_safe` - ไฟล์ .htaccess ที่ปลอดภัยสำหรับ public
3. `public/basic_test.html` - ทดสอบ HTML
4. `public/simple_test.php` - ทดสอบ PHP (แก้ไขแล้ว)

## ลำดับการแก้ไข

1. ✅ ปิด .htaccess ทั้งหมด
2. ✅ ทดสอบ HTML
3. ✅ ทดสอบ PHP
4. ✅ เปิด .htaccess ที่ปลอดภัย
5. ✅ ทดสอบ Login Page

## หากยังไม่ได้

ติดต่อ hosting provider พร้อมข้อมูล:
- Error: 500 Internal Server Error
- Server: Apache/Nginx
- PHP Version: ต้องการ 7.4+
- Modules: mod_rewrite, mod_headers
- Path: `/var/www/vhosts/nkslgroup.com/httpdocs/container_system/`
