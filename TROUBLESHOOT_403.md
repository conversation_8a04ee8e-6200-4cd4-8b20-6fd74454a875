# แก้ไขปัญหา 403 Forbidden Error

## ปัญหาที่พบ
เว็บไซต์ขึ้น "403 Forbidden" เมื่อเข้าถึง https://nkslgroup.com/container_system/public/login.php

## สาเหตุที่เป็นไปได้

### 1. File Permissions ไม่ถูกต้อง
```bash
# ตรวจสอบ permissions
ls -la public/

# แก้ไข permissions
chmod 755 public/
chmod 644 public/*.php
chmod 644 public/.htaccess
```

### 2. Directory Structure ไม่ถูกต้อง
ตรวจสอบว่าไฟล์อยู่ในตำแหน่งที่ถูกต้อง:
```
/path/to/website/
├── container_system/
│   ├── public/
│   │   ├── login.php
│   │   ├── index.php
│   │   └── .htaccess
│   ├── includes/
│   └── database/
```

### 3. .htaccess ที่เข้มงวดเกินไป
ลองเปลี่ยนชื่อ .htaccess เป็น .htaccess_backup ชั่วคราว:
```bash
mv public/.htaccess public/.htaccess_backup
```

### 4. Web Server Configuration
ตรวจสอบว่า web server อนุญาตให้เข้าถึงไดเรกทอรี

## วิธีแก้ไข

### ขั้นตอนที่ 1: ทดสอบไฟล์พื้นฐาน
1. เข้าถึง: https://nkslgroup.com/container_system/public/test.php
2. ถ้าทำงาน = ปัญหาอยู่ที่ไฟล์เฉพาะ
3. ถ้าไม่ทำงาน = ปัญหาอยู่ที่ permissions หรือ server config

### ขั้นตอนที่ 2: ตรวจสอบ Permissions
```bash
# รันคำสั่งนี้ในโฟลเดอร์ container_system
chmod +x fix_permissions.sh
./fix_permissions.sh
```

### ขั้นตอนที่ 3: ทดสอบโดยไม่มี .htaccess
```bash
# เปลี่ยนชื่อ .htaccess ชั่วคราว
mv public/.htaccess public/.htaccess_backup

# ทดสอบเข้าถึงเว็บไซต์
# ถ้าทำงาน = ปัญหาอยู่ที่ .htaccess
```

### ขั้นตอนที่ 4: ตรวจสอบ Error Logs
```bash
# ตรวจสอบ Apache error log
tail -f /var/log/apache2/error.log

# หรือ
tail -f /var/log/httpd/error_log
```

## การทดสอบทีละขั้นตอน

### 1. ทดสอบไฟล์ test.php
URL: https://nkslgroup.com/container_system/public/test.php

### 2. ทดสอบไฟล์ index.php
URL: https://nkslgroup.com/container_system/public/index.php

### 3. ทดสอบไฟล์ login.php
URL: https://nkslgroup.com/container_system/public/login.php

## คำสั่งที่ใช้บ่อย

### ตรวจสอบ Permissions
```bash
ls -la public/
ls -la includes/
ls -la database/
```

### แก้ไข Permissions
```bash
# สำหรับ public directory
chmod 755 public/
chmod 644 public/*.php

# สำหรับ includes directory  
chmod 700 includes/
chmod 644 includes/*.php

# สำหรับ database directory
chmod 700 database/
chmod 644 database/*.sql
```

### ตรวจสอบ Ownership
```bash
ls -la
# ถ้าจำเป็นต้องเปลี่ยน owner
# chown -R www-data:www-data .
# หรือ
# chown -R apache:apache .
```

## หากยังแก้ไขไม่ได้

### 1. ติดต่อ Hosting Provider
- แจ้งปัญหา 403 Forbidden
- ขอให้ตรวจสอบ server configuration
- ขอให้ตรวจสอบ mod_rewrite และ .htaccess support

### 2. ตรวจสอบ PHP Version
- ระบบต้องการ PHP 7.4+
- ตรวจสอบว่า PHP modules ที่จำเป็นติดตั้งแล้ว

### 3. ตรวจสอบ Database Connection
- ตรวจสอบ username/password ใน config.php
- ตรวจสอบว่า database server เข้าถึงได้

## ข้อมูลสำหรับ Support

### Server Information
- URL: https://nkslgroup.com/container_system/public/login.php
- Error: 403 Forbidden
- Framework: PHP/MySQL
- Required PHP: 7.4+

### Files Structure
```
container_system/
├── public/ (755)
│   ├── *.php (644)
│   └── .htaccess (644)
├── includes/ (700)
│   └── *.php (644)
└── database/ (700)
    └── *.sql (644)
```
