-- Migration script to add updated_at field to jobs table
-- Run this script to update existing database installations

-- Add updated_at column to jobs table if it doesn't exist
ALTER TABLE jobs
ADD COLUMN IF NOT EXISTS updated_at DATETIME DEFAULT NULL
AFTER created_at;

-- Update existing records to set updated_at = created_at for consistency
UPDATE jobs SET updated_at = NOW() WHERE updated_at IS NULL;

-- Verify the migration
SELECT 'Migration completed successfully' as status;
SELECT COUNT(*) as total_jobs_with_updated_at FROM jobs WHERE updated_at IS NOT NULL;
