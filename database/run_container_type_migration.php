<?php
/**
 * Migration script สำหรับอัปเดต container_type ENUM
 * รันไฟล์นี้เพื่ออัปเดตฐานข้อมูลให้รองรับประเภทตู้ใหม่
 */

require_once '../includes/db.php';

echo "<h2>Container Type Migration</h2>";
echo "<p>Starting migration to update container_type ENUM values...</p>";

try {
    // เริ่ม transaction
    $pdo->beginTransaction();
    
    echo "<p>Step 1: Adding temporary column...</p>";
    $pdo->exec("ALTER TABLE jobs ADD COLUMN container_type_new ENUM('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว')");
    
    echo "<p>Step 2: Converting existing data...</p>";
    $stmt = $pdo->exec("UPDATE jobs SET container_type_new = 
        CASE 
            WHEN container_type = 'เปล่า' THEN 'เปล่ายาว'
            WHEN container_type = 'หนัก' THEN 'หนักยาว'
            ELSE 'เปล่ายาว'
        END
    WHERE container_type IS NOT NULL");
    echo "<p>Updated $stmt rows</p>";
    
    echo "<p>Step 3: Dropping old column...</p>";
    $pdo->exec("ALTER TABLE jobs DROP COLUMN container_type");
    
    echo "<p>Step 4: Renaming new column...</p>";
    $pdo->exec("ALTER TABLE jobs CHANGE container_type_new container_type ENUM('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว')");
    
    echo "<p>Step 5: Updating service fees...</p>";
    $stmt = $pdo->exec("UPDATE jobs SET service_fee = 
        CASE 
            WHEN container_type = 'เปล่าสั้น' THEN '214'
            WHEN container_type = 'เปล่ายาว' THEN '350'
            WHEN container_type = 'หนักสั้น' THEN '900'
            WHEN container_type = 'หนักยาว' THEN '1200'
            ELSE service_fee
        END
    WHERE container_type IS NOT NULL");
    echo "<p>Updated service fees for $stmt rows</p>";
    
    // Commit transaction
    $pdo->commit();
    
    echo "<div style='color: green; font-weight: bold;'>";
    echo "<p>✅ Migration completed successfully!</p>";
    echo "<p>Container type ENUM has been updated to support:</p>";
    echo "<ul>";
    echo "<li>เปล่าสั้น (214 บาท)</li>";
    echo "<li>เปล่ายาว (350 บาท)</li>";
    echo "<li>หนักสั้น (900 บาท)</li>";
    echo "<li>หนักยาว (1200 บาท)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    // Rollback on error
    $pdo->rollback();
    echo "<div style='color: red; font-weight: bold;'>";
    echo "<p>❌ Migration failed: " . $e->getMessage() . "</p>";
    echo "<p>All changes have been rolled back.</p>";
    echo "</div>";
}

echo "<p><a href='../public/dashboard.php'>← Back to Dashboard</a></p>";
?>
