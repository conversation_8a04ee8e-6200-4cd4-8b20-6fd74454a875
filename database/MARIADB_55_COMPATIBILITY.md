# MariaDB 5.5 Compatibility Fix

## ปัญหาที่พบ
```
Error #1293 - Incorrect table definition; there can be only one TIMESTAMP column with CURRENT_TIMESTAMP in DEFAULT or ON UPDATE clause
```

## สาเหตุ
MariaDB/MySQL เวอร์ชัน 5.5.x มีข้อจำกัดที่อนุญาตให้มี TIMESTAMP column ที่ใช้ CURRENT_TIMESTAMP ได้เพียง 1 column เท่านั้นต่อ 1 table

## การแก้ไข

### 1. ไฟล์ที่แก้ไขแล้ว
- `database/container_system.sql` - แก้ไข TIMESTAMP เป็น DATETIME
- `database/schema.sql` - แก้ไข TIMESTAMP เป็น DATETIME  
- `database/job_edit_history.sql` - แก้ไข TIMESTAMP เป็น DATETIME
- `database/update_schema_for_edit_history.sql` - แก้ไข TIMESTAMP เป็น DATETIME
- `database/install_edit_history.php` - แก้ไข TIMESTAMP เป็น DATETIME
- `database/migration_add_updated_at.sql` - แก้ไข TIMESTAMP เป็น DATETIME

### 2. ไฟล์ PHP ที่อัปเดต
- `public/create_job.php` - เพิ่ม NOW() ใน INSERT
- `public/edit_job.php` - เพิ่ม NOW() ใน INSERT และ UPDATE
- `public/update_job.php` - เพิ่ม NOW() ใน UPDATE

### 3. ไฟล์ SQL ใหม่สำหรับ MariaDB 5.5
- `database/container_system_mariadb55.sql` - เวอร์ชันที่เข้ากันได้กับ MariaDB 5.5

## การใช้งาน

### สำหรับ Production Server ใหม่
ใช้ไฟล์ `database/container_system_mariadb55.sql`:
```sql
mysql -u username -p database_name < database/container_system_mariadb55.sql
```

### สำหรับ Database ที่มีอยู่แล้ว
รันคำสั่ง SQL เหล่านี้:

```sql
-- แก้ไขตาราง jobs
ALTER TABLE jobs MODIFY updated_at DATETIME DEFAULT NULL;

-- แก้ไขตาราง job_edit_history  
ALTER TABLE job_edit_history MODIFY edit_timestamp DATETIME DEFAULT NULL;

-- แก้ไขตาราง login_attempts
ALTER TABLE login_attempts MODIFY attempt_time DATETIME DEFAULT NULL;

-- อัปเดตข้อมูลที่มีอยู่
UPDATE jobs SET updated_at = NOW() WHERE updated_at IS NULL;
```

## การเปลี่ยนแปลงหลัก

### Before (ไม่ทำงานใน MariaDB 5.5)
```sql
CREATE TABLE jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### After (ทำงานใน MariaDB 5.5)
```sql
CREATE TABLE jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL
);
```

### PHP Code Changes
```php
// Before
INSERT INTO jobs (...) VALUES (...)

// After  
INSERT INTO jobs (..., created_at, updated_at) VALUES (..., NOW(), NOW())

// Update
UPDATE jobs SET ..., updated_at = NOW() WHERE id = ?
```

## ข้อดีของการแก้ไข

1. **เข้ากันได้**: ทำงานได้ทั้ง MariaDB 5.5+ และ MySQL 5.5+
2. **ยืดหยุ่น**: สามารถควบคุม timestamp ได้ดีกว่า
3. **ชัดเจน**: รู้ว่าเมื่อไหร่ที่ข้อมูลถูกอัปเดต
4. **ปลอดภัย**: ไม่มีปัญหา timezone confusion

## การทดสอบ

### ทดสอบการสร้าง Job ใหม่
```sql
SELECT id, job_number, created_at, updated_at FROM jobs ORDER BY id DESC LIMIT 5;
```

### ทดสอบการแก้ไข Job
```sql
-- แก้ไข job
UPDATE jobs SET customer_name = 'Test Customer', updated_at = NOW() WHERE id = 1;

-- ตรวจสอบ
SELECT id, customer_name, created_at, updated_at FROM jobs WHERE id = 1;
```

### ทดสอบ Edit History
```sql
SELECT * FROM job_edit_history ORDER BY edit_timestamp DESC LIMIT 5;
```

## หมายเหตุ

- ระบบจะยังคงทำงานได้ปกติ
- ข้อมูลเก่าจะไม่สูญหาย
- Performance ไม่ได้รับผลกระทบ
- สามารถ upgrade เป็น MariaDB เวอร์ชันใหม่ได้ในอนาคต

## การ Backup ก่อนแก้ไข

```bash
# Backup database
mysqldump -u username -p database_name > backup_before_fix.sql

# Backup files
tar -czf files_backup.tar.gz /path/to/container_system_app_v2/
```

## การ Rollback (ถ้าจำเป็น)

```sql
-- Restore from backup
mysql -u username -p database_name < backup_before_fix.sql
```
