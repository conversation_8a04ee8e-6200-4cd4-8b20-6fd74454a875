-- อัปเดต ENUM ของ container_type ให้รองรับค่าใหม่
-- ไฟล์นี้ใช้สำหรับอัปเดตฐานข้อมูลที่มีอยู่แล้ว

-- ขั้นตอนที่ 1: เพิ่มคอลัมน์ชั่วคราว
ALTER TABLE jobs ADD COLUMN container_type_new ENUM('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว');

-- ขั้นตอนที่ 2: แปลงข้อมูลเก่าให้เป็นรูปแบบใหม่
UPDATE jobs SET container_type_new = 
    CASE 
        WHEN container_type = 'เปล่า' THEN 'เปล่ายาว'
        WHEN container_type = 'หนัก' THEN 'หนักยาว'
        ELSE 'เปล่ายาว'  -- ค่าเริ่มต้นสำหรับข้อมูลที่ไม่ตรงกับเงื่อนไข
    END
WHERE container_type IS NOT NULL;

-- ขั้นตอนที่ 3: ลบคอลัมน์เก่า
ALTER TABLE jobs DROP COLUMN container_type;

-- ขั้นตอนที่ 4: เปลี่ยนชื่อคอลัมน์ใหม่
ALTER TABLE jobs CHANGE container_type_new container_type ENUM('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว');

-- ขั้นตอนที่ 5: อัปเดต service_fee ให้ตรงกับประเภทตู้ใหม่
UPDATE jobs SET service_fee = 
    CASE 
        WHEN container_type = 'เปล่าสั้น' THEN '214'
        WHEN container_type = 'เปล่ายาว' THEN '350'
        WHEN container_type = 'หนักสั้น' THEN '900'
        WHEN container_type = 'หนักยาว' THEN '1200'
        ELSE service_fee  -- เก็บค่าเดิมถ้าไม่ตรงกับเงื่อนไข
    END
WHERE container_type IS NOT NULL;
