-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:8889
-- Generation Time: Sep 15, 2025 at 03:41 PM
-- Server version: 8.0.40
-- PHP Version: 8.3.14

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `container_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `customer_names`
--

CREATE TABLE `customer_names` (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `customer_names`
--

INSERT INTO `customer_names` (`id`, `name`) VALUES
(1, '3J'),
(2, 'Aeroklas'),
(3, 'AJJ LOGISTICS'),
(4, 'AMERICA METAL'),
(5, 'ANJI-NYK'),
(6, 'APL LOGISTICS'),
(7, 'ASIA GOLDEN RICE'),
(8, 'ASIA LOGISTICS'),
(9, 'ASIA SHIPPING'),
(10, 'ASS'),
(11, 'BGC'),
(12, 'BGF'),
(13, 'BLUEDA'),
(14, 'Borneo'),
(15, 'BUDGET LOGISTICS'),
(16, 'C.K. Group'),
(17, 'CCSCM'),
(18, 'CHAIWATTANA'),
(19, 'CP Poly'),
(20, 'DB Schenker'),
(21, 'DSV-Srivichai'),
(22, 'ECU'),
(23, 'EPS'),
(24, 'Fast Inter'),
(25, 'FCN'),
(26, 'FM'),
(27, 'FTA'),
(28, 'FUNAI'),
(29, 'Haier'),
(30, 'Hellman'),
(31, 'ICL'),
(32, 'JANETIWA'),
(33, 'KERRY LOGISTICS'),
(34, 'KERRY-APEX'),
(35, 'KOBCHAI'),
(36, 'Korn Thai'),
(37, 'LIXIL'),
(38, 'MGH'),
(39, 'MPJ DC'),
(40, 'MTP'),
(41, 'Norzin'),
(42, 'NRG'),
(43, 'OOR Express'),
(44, 'OPL'),
(45, 'P&I 99'),
(46, 'P.W. Eng.'),
(47, 'PAC ORIENT'),
(48, 'PARIDA'),
(49, 'PCN'),
(50, 'PFI'),
(51, 'Renown'),
(52, 'Renown LOG'),
(53, 'Riken'),
(54, 'RPS Technologies'),
(55, 'SC88'),
(56, 'SHERA'),
(57, 'Sino'),
(58, 'SKC'),
(59, 'Srivichai'),
(60, 'SWI'),
(61, 'T&T'),
(62, 'TAIGA'),
(63, 'Thai Acrylic'),
(64, 'THAI LEE'),
(65, 'Toshiba'),
(66, 'Unico'),
(67, 'Union Ocean'),
(68, 'Unithai'),
(69, 'WHALE TRANS'),
(70, 'Yusen');

-- --------------------------------------------------------

--
-- Table structure for table `depositor_names`
--

CREATE TABLE `depositor_names` (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `depositor_names`
--

INSERT INTO `depositor_names` (`id`, `name`) VALUES
(2, 'ขวัญชัย การบรรจง'),
(22, 'คำไพ ถินแก้ว'),
(6, 'จักรพงษ์ รัตนวงษา'),
(16, 'จินดามนต์ ชูเลิศ'),
(5, 'ชัยยงค์ กะรนรักษ์'),
(20, 'ทินกร แซ่ก๊วย'),
(7, 'ธีระพล กุลจะนุช'),
(21, 'นิคม ขันไพบูลย์'),
(9, 'นิรันด์ ดาทอง'),
(1, 'บัญชา คำทุย'),
(14, 'ประกอบ สังเวียนวงศ์'),
(15, 'พิทักษ์ เดือนไธสง'),
(13, 'ภาสกร สมภักดี'),
(19, 'ภิญโญ เชื้อสูงเนิน'),
(4, 'วศิธรณ์ จันทร์ดาวัน'),
(11, 'สวัสดิ์ ยางศิลา'),
(17, 'สิงห์ สีธงชัย'),
(10, 'สุทธิศักดิ์ สมภักดี'),
(8, 'สุทิน วรวุธ'),
(18, 'สุรพงษ์ ศิริเอนก'),
(12, 'เสน่ห์ สมภักดี'),
(3, 'โชติวิทย์ สินสุพรรณ์');

-- --------------------------------------------------------

--
-- Table structure for table `deposit_places`
--

CREATE TABLE `deposit_places` (
  `id` int NOT NULL,
  `place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `deposit_places`
--

INSERT INTO `deposit_places` (`id`, `place`) VALUES
(1, '99 CON'),
(2, 'A-ONE'),
(7, 'CCIS1'),
(8, 'CCIS4'),
(9, 'CCIS7'),
(5, 'CDS'),
(22, 'CELLO BY HAST'),
(6, 'CENTRAL'),
(3, 'CIMC1'),
(4, 'CIMC2'),
(10, 'COSIAM5'),
(11, 'D DEPOT'),
(12, 'ECD1'),
(13, 'ECD2'),
(14, 'ECONIC'),
(15, 'G-FORTUNE1'),
(16, 'G-FORTUNE2'),
(17, 'G-FORTUNE3'),
(18, 'G-FORTUNE4'),
(19, 'G-FORTUNE5A'),
(20, 'G-FORTUNE5B'),
(21, 'HAST1'),
(25, 'HK1'),
(26, 'HK2'),
(23, 'HLA'),
(24, 'KCST4'),
(29, 'KLN DEPOT'),
(27, 'KRC'),
(28, 'KSP'),
(33, 'LID'),
(52, 'MAHAPORN'),
(53, 'MATCH BOX'),
(31, 'MCCT'),
(35, 'MEDLOG1'),
(36, 'MEDLOG3'),
(32, 'MPJ'),
(34, 'OM'),
(37, 'PH'),
(39, 'PISUT'),
(38, 'REI'),
(40, 'SCS1'),
(41, 'SCS2'),
(30, 'SIAMCOM DEPOT'),
(42, 'SMART1'),
(43, 'SMART2'),
(44, 'SMART3'),
(45, 'SMART4'),
(47, 'TICS'),
(46, 'TIPS'),
(48, 'UNIWISE'),
(49, 'WINWIN1'),
(50, 'WINWIN2'),
(51, 'YJC');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` int NOT NULL,
  `job_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` int NOT NULL,
  `job_date` date DEFAULT NULL,
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `job_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `depositor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `booking_bl` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `container_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `container_size` enum('20','40') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `first_return_date` date DEFAULT NULL,
  `closing_date` date DEFAULT NULL,
  `closing_time` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `return_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `deposit_date` date DEFAULT NULL,
  `deposit_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `container_type` enum('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว') COLLATE utf8mb4_general_ci DEFAULT NULL,
  `service_fee` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `final_return_date` date DEFAULT NULL,
  `return_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('Pending','Done') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Pending',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`id`, `job_number`, `user_id`, `job_date`, `customer_name`, `user_name`, `job_title`, `depositor_name`, `booking_bl`, `container_no`, `container_size`, `first_return_date`, `closing_date`, `closing_time`, `return_place`, `deposit_date`, `deposit_place`, `created_at`, `updated_at`, `container_type`, `service_fee`, `final_return_date`, `return_user`, `status`, `remark`) VALUES
(1, 'job-20250618-0199', 1, '2025-06-18', 'Toshiba', 'admin', 'Toshiba', 'สุกิจ', 'BL123456789', 'AAAA1234567', '40', '2025-06-18', '2025-06-20', NULL, 'ท่าจอดรถ', '2025-06-18', 'โรงงาน Toshiba', '2025-06-18 16:40:58', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-06-19', 'สุกิจ', 'Done', ''),
(2, 'job-20250618-5370', 1, '2025-06-18', 'Renow', 'admin', 'Renow', 'สุกิจ', 'BL012345678', 'BBBB1234567', '20', '2025-06-19', '2025-06-21', NULL, 'ท่าจอดรถ', '2025-06-18', 'โรงงาน Renow', '2025-06-18 17:20:47', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-07-30', 'admin', 'Done', ''),
(3, 'job-20250620-4832', 1, '2025-06-20', 'Toshiba', 'admin', 'Toshiba', 'สุกิจ', 'BN000222222', 'AAAA1122222', '40', '2025-06-17', '2025-06-24', NULL, 'ท่าจอดรถแหลม', '2025-06-24', 'โรงงาน Toshiba แหลม', '2025-06-20 15:35:54', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-07-30', 'admin', 'Done', ''),
(4, 'job-20250902-9921', 1, '2025-09-02', 'OPL', 'admin', 'OPL', 'สิงห์', 'BL987654321', 'CO987654321', '20', '2025-09-02', '2025-09-03', '18:30', 'ปากช่อง', '2025-09-02', 'ชลบุรี', '2025-09-02 07:13:03', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-09-03', 'admin', 'Done', 'แก้ไขเลขตู้'),
(5, 'job-20250903-3395', 1, '2025-09-03', 'unico', 'admin', 'unico', 'กิจ', 'BL0009876543', 'BL000987654', '40', '2025-09-03', '2025-09-04', '18:50', 'เมืองชลบุรี', '2025-09-03', 'ชลบุรี', '2025-09-03 13:40:42', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-09-03', 'admin', 'Done', 'คืนตู้แล้ว'),
(6, 'job-20250903-9082', 1, '2025-09-03', 'OPL', 'admin', 'OPLdd', 'กิจ', 'BL9876543213', 'CO987654321', '40', '2025-09-04', '2025-09-04', '14:00', '', '2025-09-03', 'โรงงาน Toshiba แหลม', '2025-09-03 14:25:49', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-09-03', 'กิจ', 'Done', 'คืนตู้'),
(7, 'job-20250903-6278', 1, '2025-09-03', 'OPL', 'admin', 'OPL', 'กิจ', 'BL9876543213', 'CO987654321', '20', '2025-09-03', '2025-09-04', '14:10', 'ท่าจอดรถ', '2025-09-03', 'โรงงาน Renow', '2025-09-03 14:30:25', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-09-03', 'admin', 'Done', ''),
(9, 'job-20250906-7617', 2, '2025-09-06', 'OPL', 'userkij', 'OPL', 'กิจ', 'BL7898765432', 'BBBB9876543', '20', '2025-09-06', '2025-09-10', '15:20', 'ท่าจอดรถแหลม', '2025-09-07', 'โรงงาน Toshiba แหลม', '2025-09-06 12:01:39', '2025-09-15 12:30:50', 'เปล่ายาว', '350', '2025-09-15', 'admin', 'Done', ''),
(10, 'job-20250915-9435', 1, '2025-09-15', '3J', 'admin', '3J to Port', 'คำไพ ถินแก้ว', '', 'AAAA9875787', '20', '2025-09-15', '2025-09-17', '04:20', 'LCB-A0', '2025-09-16', 'A-ONE', '2025-09-15 12:31:12', '2025-09-15 13:52:55', 'หนักสั้น', '535', NULL, '', 'Pending', '');

-- --------------------------------------------------------

--
-- Table structure for table `job_edit_history`
--

CREATE TABLE `job_edit_history` (
  `id` int NOT NULL,
  `job_id` int NOT NULL,
  `user_id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `field_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `old_value` text COLLATE utf8mb4_general_ci,
  `new_value` text COLLATE utf8mb4_general_ci,
  `edit_timestamp` datetime DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_general_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `job_edit_history`
--

INSERT INTO `job_edit_history` (`id`, `job_id`, `user_id`, `username`, `field_name`, `old_value`, `new_value`, `edit_timestamp`, `ip_address`, `user_agent`) VALUES
(1, 4, 1, 'admin', 'container_no', '', 'CO987654321', '2025-09-02 07:13:49', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(2, 4, 1, 'admin', 'return_user', NULL, '<br /><b>Deprecated</b>:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in <b>/Applications/MAMP/htdocs/container_system_app_v2/public/edit_job.php</b> on line <b>511</b><br />', '2025-09-02 07:13:49', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(3, 4, 1, 'admin', 'remark', NULL, '', '2025-09-02 07:13:49', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(4, 4, 1, 'admin', 'remark', '', 'แก้ไขเลขตู้', '2025-09-02 07:14:16', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(5, 6, 1, 'admin', 'customer_name', '', 'OPL', '2025-09-03 15:04:57', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(6, 7, 1, 'admin', 'job_title', '', 'OPL', '2025-09-03 15:05:11', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(8, 10, 1, 'admin', 'first_return_date', NULL, '2025-09-15', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(9, 10, 1, 'admin', 'closing_date', NULL, '2025-09-16', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(10, 10, 1, 'admin', 'closing_time', NULL, '04:20', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(11, 10, 1, 'admin', 'deposit_date', NULL, '2025-09-17', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(12, 10, 1, 'admin', 'deposit_place', '', 'A-ONE', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(13, 10, 1, 'admin', 'return_user', NULL, '', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(14, 10, 1, 'admin', 'remark', NULL, '', '2025-09-15 12:46:17', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(15, 10, 1, 'admin', 'closing_date', '2025-09-16', '2025-09-17', '2025-09-15 12:47:40', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(16, 10, 1, 'admin', 'deposit_date', '2025-09-17', '2025-09-16', '2025-09-15 12:47:40', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15'),
(17, 10, 1, 'admin', 'service_fee', '900', '535', '2025-09-15 13:52:55', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15');

-- --------------------------------------------------------

--
-- Stand-in structure for view `job_edit_summary`
-- (See below for the actual view)
--
CREATE TABLE `job_edit_summary` (
`customer_name` varchar(255)
,`job_id` int
,`job_number` varchar(50)
,`last_edit_time` timestamp
,`last_editor` varchar(50)
,`total_edits` bigint
,`unique_editors` bigint
);

-- --------------------------------------------------------

--
-- Table structure for table `job_titles`
--

CREATE TABLE `job_titles` (
  `id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `job_titles`
--

INSERT INTO `job_titles` (`id`, `title`) VALUES
(3218, '3C to Port'),
(2829, '3J to Port'),
(3262, '3M to Port'),
(3163, 'A ONE AUTO TRADING to Port'),
(3241, 'ACUSHNET to Port'),
(3142, 'ACV RICH BKK Port'),
(3132, 'ADD SUSPENSION to Port'),
(3011, 'Air International to Port'),
(2898, 'Alpha10 to Port'),
(2895, 'Alpha6 to Port'),
(2899, 'Alpha9 to Port'),
(2836, 'AMERICA METAL to Port'),
(3171, 'AMPAWA to PAT'),
(2885, 'ANLU to Port'),
(2845, 'ASIA GOLDEN RICE to Port'),
(3143, 'ASIA PRECAST PRODUCTS CO.,LTD.'),
(3065, 'ATEC PRODUCTION to Port'),
(3068, 'AU&ARK to Port'),
(2915, 'AUNDE to Port'),
(3088, 'AURORA WISDOM to Port'),
(2877, 'Beer Thip Brewery\'s to Port'),
(2870, 'BGC to Port (AGI)'),
(2872, 'BGC to Port (PTI)'),
(2871, 'BGC to Port (RBI)'),
(2873, 'BGF (Kabinburi) to Port'),
(2874, 'BGF (Kabinburi)-RANONG to Port'),
(2875, 'BGF to Port'),
(3146, 'BKK Port to อยุธยา'),
(2961, 'BO TONG to NONTHABURI (Bee)'),
(3264, 'BYD (Export) to Port'),
(3295, 'BYD to Port'),
(3076, 'Canadian to Port'),
(3149, 'Carpenter to Suvarnabhumi airport'),
(3217, 'CB TACT to Port'),
(2909, 'Century to Port'),
(2887, 'CHAIWATTANA to Port'),
(2927, 'Chan Yai to Port (Bee)'),
(3039, 'CHINUTS AGRITECH to Port'),
(2902, 'CJ to Port'),
(2888, 'CP Poly to Port'),
(3023, 'CYC INTERNATIONAL to Port'),
(2859, 'DANA SPICER to Port'),
(2986, 'Daramic to Port'),
(2890, 'DBS to Port'),
(2894, 'DHL to Port'),
(3033, 'DOLPHIN WORLD to Port'),
(3175, 'ECOS to Port'),
(3201, 'EL-Araby to Port'),
(3186, 'EMPTY CONTAINER TO PORT'),
(3029, 'ENAXUS to Port'),
(3047, 'EX-VALUE (น้ำตาล) to Port'),
(2960, 'FILTRONA to Port'),
(3211, 'FMT to Port'),
(2893, 'FORD KNT to Port'),
(3159, 'FORWARD PACKAGING to SVB PACKING'),
(2958, 'FRUITTARA to Port'),
(3224, 'Fuding Industries to Port'),
(2976, 'FUNAI to Port'),
(3225, 'Gangyan Diamond to Port'),
(3129, 'Gen Bev (บ้านแพ้ว) to Port'),
(2990, 'General Rubber to Port'),
(3215, 'GIANT BANGSAN to Port'),
(2853, 'GIANTLOK to Port'),
(3057, 'GLP to Port'),
(3070, 'GOLD MARK to Port'),
(3074, 'GOLDCITY FOOTTECH to Port'),
(3214, 'GOLDEN SEA to Port'),
(2977, 'Haier to Port'),
(3290, 'HAKUZO to Port'),
(3028, 'HIM to Port'),
(3139, 'Hitachi to Port'),
(3082, 'HUA LU DE to Port'),
(2841, 'Huayi to Port'),
(3038, 'Hydroblok Grand to Port'),
(3239, 'ILC KM.18 to Port'),
(2916, 'Imanaka to Port'),
(3141, 'IPP to Port'),
(3133, 'ISUZU to Port'),
(3092, 'IT LUGGAGE to Port'),
(3066, 'JOON CHEE INDUSTRIAL to Port'),
(3098, 'JUST STONE to Port'),
(3083, 'K.K.GLOVE to Port'),
(3064, 'K.S.P. EQUIPMENT to Port'),
(3019, 'KANEKA to Port'),
(2866, 'KANKAY GARDEN to Port'),
(3069, 'KASEM PLASTIC to Port'),
(3137, 'KASEMCHAIFOOD to Port'),
(2971, 'Kdd To Port'),
(3287, 'Kellogg to Port'),
(3123, 'KJ WORLD FOODS to Port'),
(2844, 'KNT to Port'),
(3006, 'Korn Thai to Port'),
(3274, 'KOSEN to Port'),
(3158, 'KRUNGTHEP AUTO to Port'),
(3169, 'KRUNGTHEP AUTO(SRIRACHA)to Port'),
(2995, 'KUBOTA ENGINE to Port'),
(3051, 'L.LIGHTING GLASS to Port'),
(3121, 'LCH Port to กิ่งแก้ว (ยาง)'),
(3110, 'LCL กระบี่'),
(3062, 'LIGHT & HOPE to Port'),
(3045, 'Lightup Creation (Rayong) to Port'),
(3060, 'Lightup Creation to Port'),
(3061, 'Lightup Creation to Port (2 Load)'),
(3007, 'LIXIL to Port'),
(3153, 'LKB Port to Actochem (นครนายก) (77km)'),
(3167, 'LKB Port to LCB'),
(3059, 'LOVE SPEEDS to Port'),
(2905, 'MANN&HUMMEL to Port'),
(3091, 'MARK AND FRIEND to Port'),
(3161, 'MARK MOTORS to Port'),
(3009, 'Maxion Wheel to Port'),
(2850, 'MENTEC to Port'),
(3018, 'Midea (MTP) to Port'),
(3222, 'Midea to Port'),
(2896, 'Midea-WH to port'),
(2912, 'MIT to Port'),
(3204, 'Mitsui to Port'),
(2981, 'MONAMI to Port'),
(2856, 'MR.YANG To Port'),
(3014, 'MTP to Port'),
(2954, 'N.J. MODERN STEEL'),
(3027, 'Norzin (บางปะกง) to Port'),
(3026, 'Norzin (สมุทรสาคร) to Port'),
(2855, 'NORZIN IM&EX TRADE to Port'),
(3024, 'Norzin to Port'),
(2860, 'NUTRIX to Port'),
(3058, 'Ocean Bangpla to Port'),
(2959, 'OSAWA to Port'),
(3130, 'Ovofoodtech to Port'),
(2978, 'P&G to Port'),
(3173, 'PANUS ASSEMBLY to Port'),
(3174, 'PK Marine Tranding to Port'),
(3003, 'PLASTIC EXTRUSIONS to Port'),
(3155, 'PONPROUD to Port'),
(3263, 'Port to 3M'),
(2910, 'Port to 99 Container'),
(3179, 'Port to A.J. PLAST'),
(3135, 'Port to ADD SUSPENSION'),
(2833, 'Port to Aeroklas'),
(2834, 'Port to AJJ LOGISTICS'),
(3278, 'Port to ALCONIX'),
(2862, 'Port to ALL GRAND TRADING'),
(2980, 'Port to ALMENDRA'),
(2840, 'Port to AMERICA (ฉะเชิงเทรา)'),
(2839, 'Port to AMERICA (บางเลน)'),
(2837, 'Port to AMERICA (เกาะขนุน)'),
(2835, 'Port to AMERICA METAL'),
(3116, 'Port to AMPACET'),
(2852, 'Port to ANT (BANGKOK)'),
(3228, 'Port to ASHBURN'),
(2917, 'Port to AUNDE'),
(2975, 'Port to BANG PHAE'),
(3148, 'Port to BANGPHI'),
(3136, 'Port to Bangrak'),
(3162, 'Port to BATHIC (ส.ประภาศิลป)'),
(2999, 'Port to BEST BORN'),
(3100, 'Port to BLUE DRAGON'),
(3288, 'Port to BMW'),
(3289, 'Port to BMW (Drop YLLC)'),
(2933, 'Port to BNS-บ่อวิน (Bee)'),
(3081, 'Port to BOYUAN METAL'),
(2931, 'Port to BROADWAY PRECISION (Bee)'),
(3270, 'Port to BYD'),
(3048, 'Port to CALCIUM CHLORIDE'),
(3054, 'Port to CALDWELL'),
(3096, 'Port to CARCO ENGINEERING'),
(3151, 'Port to Carpenter'),
(2881, 'Port to CCSCM WH'),
(3093, 'Port to CELESTICA (THAILAND)'),
(3073, 'Port to CHINUTS'),
(3041, 'Port to CHINUTS AGRITECH'),
(2993, 'Port to CHUBB'),
(2992, 'Port to CHYUAN LIH'),
(3243, 'Port to CNH บางพลี'),
(3071, 'Port to CRIS GO (THAILAND)'),
(3193, 'Port to Cross-Border (T&T)'),
(2867, 'Port to CROSS-BORDER E-COMMERCE'),
(2950, 'Port to CTC INTER (Bee)'),
(3229, 'Port to DALU'),
(3125, 'Port to DAY O CHICK'),
(2889, 'Port to DBS'),
(3107, 'Port to DESTER'),
(3277, 'Port to DTS'),
(3236, 'Port to DYNACHISSO'),
(3292, 'Port to Ennotrade'),
(2930, 'Port to EPS-Surat Thani (Bee)'),
(3111, 'Port to EPZ บางปู'),
(3242, 'Port to ESSILOR OPTICAL'),
(3106, 'Port to ETK'),
(2966, 'Port to EURO ASIA'),
(3227, 'Port to Everich'),
(2952, 'Port to FIRST METAL SHEET (Bee)'),
(2964, 'Port to FMP'),
(3044, 'Port to FN Outlet Petchaburi'),
(3001, 'Port to Forth'),
(3004, 'Port to FORTH EMS'),
(3296, 'Port to FUJI FILM'),
(3237, 'Port to G J Steel'),
(2861, 'Port to GENERA ASIA'),
(2940, 'Port to GG CONSTRUCTION (Bee)'),
(2851, 'Port to GIORDANO'),
(3195, 'Port to GMS CORP.'),
(3067, 'Port to GOLD MARK'),
(3030, 'Port to GOLDMORIC'),
(3279, 'Port to Great Wall Motor'),
(3114, 'Port to GXO'),
(3165, 'Port to H2O ALLDAY'),
(3293, 'Port to HANWA STEEL'),
(3235, 'Port to HazChem'),
(3103, 'Port to HELUKABEL'),
(2922, 'Port to HERNG HUI WOOD (Bee)'),
(3281, 'Port to Hino'),
(3194, 'Port to Hiroko'),
(3271, 'Port to HITACHI'),
(2968, 'Port to Hongbao'),
(3072, 'Port to HUA LU DE'),
(2972, 'Port to Huahong'),
(2947, 'Port to HUALONG (Bee)'),
(3032, 'Port to Hydroblok Grand'),
(2869, 'Port to INGREDIENTS'),
(2920, 'Port to JIALONG (Bee)'),
(2935, 'Port to JIELI BAG (Bee)'),
(3230, 'Port to Jing Gong'),
(2838, 'Port to JOY IRON & METAL'),
(2932, 'Port to Ju Thai (Bee)'),
(2948, 'Port to Ju Thai Rayong (Bee)'),
(2878, 'Port to JWD'),
(3172, 'Port to KATEVANICH INDUSTRY'),
(3286, 'Port to Kellogg'),
(2987, 'Port to KEN UNITED'),
(2863, 'Port to KERRY Logistics'),
(2949, 'Port to KEYI CONSTRUCTION (Bee)'),
(3160, 'Port to KIKI LUGGAGE'),
(3260, 'Port to KNT'),
(2868, 'Port to KORAKIT'),
(3095, 'Port to KSF TRANSPORT'),
(2847, 'Port to KUBOTA PRECISION'),
(2996, 'Port to Kulthorn Steel'),
(3021, 'Port to KYOWA'),
(3226, 'Port to Landward'),
(3284, 'Port to LAUTAN LUAS'),
(2946, 'Port to LCTT TRADING (Bee)'),
(2979, 'Port to LEAR AUTOMOTIVE'),
(2983, 'Port to LEAR CORPORATION'),
(3102, 'Port to LENSO'),
(3063, 'Port to LIGHT & HOPE'),
(3089, 'Port to Lightup Creation (Rayong)'),
(3008, 'Port to LIXIL'),
(2985, 'Port to LOIUSE T.'),
(3164, 'Port to LUCKY UNIFORM'),
(2967, 'Port to MA CHAROEN 1'),
(3183, 'Port to MACCAFERRI'),
(3075, 'Port to MARK AND FRIEND'),
(3078, 'Port to MARKETING 1688'),
(3276, 'Port to Materials'),
(3031, 'Port to MEDIC PACK'),
(2998, 'Port to MEGACELL'),
(3269, 'Port to Metal One'),
(3002, 'Port to Midea (Kobchai)'),
(3053, 'Port to MILD FOODS'),
(3294, 'Port to MITSUBISHI'),
(2831, 'Port to ML Optic'),
(3231, 'Port to MLT บ่อทอง'),
(3266, 'Port to NISSAN'),
(3275, 'Port to NISSAN LLC'),
(3085, 'Port to Nusa Transport'),
(3050, 'Port to O-Square'),
(3090, 'Port to O-SQUARE DISTRIBUTION'),
(3049, 'Port to Ocean Bangpla'),
(3097, 'Port to OPENSKY GLOBAL'),
(3005, 'Port to ORBIT INTERNATIONAL'),
(2982, 'Port to P&G'),
(3272, 'Port to P&G (Yusen)'),
(2943, 'Port to PENGHUI CONSTRUCTION (Bee)'),
(3118, 'Port to Pinnacle'),
(3040, 'Port to PJ Wood'),
(2969, 'Port to PRASIT VIWAT'),
(2886, 'Port to PRINX'),
(3037, 'Port to PROWOOD FACTORY'),
(3138, 'Port to PSP'),
(2919, 'Port to QD STEEL (Bee)'),
(2929, 'Port to QD STEEL บางเสาธง (Bee)'),
(3015, 'Port to Rernher'),
(2936, 'Port to RERNHER RECYCLE (Bee)'),
(2994, 'Port to SAHABHANT ELECTRIC'),
(2842, 'Port to SAIC-MOTOR'),
(2937, 'Port to SAN FINE (Bee)'),
(3232, 'Port to SANDEN (THAILAND)'),
(3094, 'Port to SANHUA INDUSTRY'),
(2830, 'Port to Saraburi'),
(2934, 'Port to SET INDUSTRIES (Bee)'),
(3273, 'Port to Setsuyo'),
(2918, 'Port to SHUNXING (Bee)'),
(3144, 'Port to Siam Dasada Khaoyai'),
(3268, 'Port to SIAM TOYOTA'),
(2944, 'Port to SIGN COMPLEX (Bee)'),
(3157, 'Port to SIRI NOBLE'),
(3101, 'Port to SOLE SOCIETY'),
(3108, 'Port to SOLE SOCIETY (ชลบุรี)'),
(2864, 'Port to SOTHON MARINE'),
(3104, 'Port to SP WELLNESS'),
(3109, 'Port to SRF Industries'),
(3043, 'Port to Sugarthai'),
(2974, 'Port to SUNGROW DEVELOPERS'),
(3034, 'Port to SUPAVUT'),
(2882, 'Port to SUZUYO'),
(2892, 'Port to SVOLT'),
(3192, 'Port to SWI'),
(3145, 'Port to T PLUS SKINCARE'),
(2846, 'Port to TAI YU PACKAGING'),
(3016, 'Port to TAN CHONG'),
(3117, 'Port to TAN CHONG (LKB)'),
(2849, 'Port to Tatsumi shokai'),
(2970, 'Port to TB TANK'),
(2941, 'Port to TEAM UNION (Bee)'),
(3112, 'Port to Teijin Polyester'),
(3198, 'Port to THAI LEE'),
(3182, 'Port to THAI TEAK WOOD VENEIR'),
(2963, 'Port to Thai Waterline'),
(2945, 'Port to THAIXING CIRCUITS (Bee)'),
(2957, 'Port to THONBURI'),
(2921, 'Port to THRIVING (Bee)'),
(2962, 'Port to Tiwanon 39'),
(3084, 'Port to TKS LOGISTICS'),
(3190, 'Port to TLBC'),
(3265, 'Port to TLC'),
(2989, 'Port to TN GROUP Store'),
(2965, 'Port to Toll'),
(3202, 'Port to Toshiba'),
(3113, 'Port to TPBI'),
(2832, 'Port to TPI Polene'),
(3189, 'Port to TPI Rayong'),
(3259, 'Port to TPV'),
(3042, 'Port to TRANSITIONS OPTICAL'),
(2984, 'Port to TRIMOTIVE'),
(3000, 'Port to TSB'),
(3080, 'Port to TY STEEL'),
(3267, 'Port to UACJ'),
(3221, 'Port to Unico'),
(3223, 'Port to Unico (Enterprise)'),
(3258, 'Port to UNIQLO'),
(3240, 'Port to Unithai KM.23'),
(2988, 'Port to Valmet Company'),
(2951, 'Port to VASEN'),
(2914, 'Port to VEM'),
(3280, 'Port to Wanhua'),
(2938, 'Port to Werun (Bee)'),
(3052, 'Port to Wongtanawoot'),
(3105, 'Port to WORK STATION'),
(2857, 'Port to World Records'),
(2939, 'Port to WORLDEX (Bee)'),
(3134, 'Port to Worldwide'),
(3012, 'Port to WUS (อยุธยา)'),
(3140, 'Port to WWE'),
(2848, 'Port to XIANGSHANG'),
(3282, 'Port to Xinggao'),
(3020, 'Port to Xtron'),
(2854, 'Port to YAMAMOTO MARK'),
(2928, 'Port to Yin-Lin & XINGYUAN'),
(3238, 'Port to Yokohama tire'),
(3086, 'Port to YONGXING STEEL'),
(3291, 'Port to ZF Auto'),
(2953, 'Port to ZHONGYANG-DELI (Bee)'),
(3115, 'Port to บางปะกง'),
(2942, 'Port to ยายจั่น Rayong (Bee)'),
(2973, 'Port to ราชบุรี'),
(3234, 'Port to ลำลูกกา'),
(3046, 'Port to วงศ์ธนาวุฒิ'),
(3120, 'Port to ไทยวัสดุ'),
(3122, 'PPT to Port'),
(3035, 'PROWOOD FACTORY to Port'),
(3185, 'RDC to Port'),
(2858, 'Re-Export MSL LOGISTICS'),
(3180, 'Riken to Port'),
(3010, 'Rohlig Logistics to Port'),
(3181, 'RPS Technologies to Port'),
(3017, 'Runergy PV Technology to Port'),
(3283, 'Sei Thai to Port'),
(2908, 'Seino Saha to Port'),
(3285, 'Sennics to Port'),
(2884, 'SENTURY to Port'),
(3208, 'SGL to Port'),
(3079, 'SHARP to Port'),
(3184, 'SHERA to Port'),
(2843, 'SIAC-CP to Depot'),
(3013, 'SIAM HITACHI to Port'),
(3150, 'SIAM KUBOTA to Port'),
(3168, 'SIAM M.C.CO to Port'),
(3245, 'SIAM STERI to Port'),
(3213, 'SIAM to Port'),
(2955, 'SIAM YOKO to Port'),
(3247, 'SIGNODE to Port'),
(3087, 'SINTANACHOTE to Port'),
(3212, 'SK ALPHA to Port'),
(3188, 'SKC to Port'),
(2880, 'SNG TAPIOCA to Port'),
(3178, 'SSW SKY WIDE to Port'),
(2997, 'Stamex Technology to Port'),
(3056, 'STARPRO STARCH to Port'),
(3166, 'SVB to Suvarnabhumi Airport'),
(2913, 'SW LOGISTICS to Port'),
(3191, 'SWI to Port'),
(3128, 'TCN to Port'),
(2911, 'TF warehouse to Port'),
(3205, 'THAI EVER to Port'),
(2891, 'Thai Gypsum to Port'),
(3147, 'Thai Ha to Suvarnabhumi airport'),
(3261, 'THAI INABA to Port'),
(3197, 'THAI LEE to Port'),
(3152, 'Thai Union Feedmill to Port'),
(3196, 'Thai-Acrylic to Port'),
(2925, 'THAICHAROEN to Port (Bee)'),
(2926, 'THAICHAROEN บ้านบึง to Port (Bee)'),
(2901, 'Thaikotch3 to port'),
(2903, 'Thaikotch4 to port'),
(2897, 'Thaikotch5 to port'),
(2865, 'TISCOM to Port'),
(2956, 'TK Foodland to Port'),
(3207, 'TKS to SCT บางบ่อ'),
(2924, 'TONGDA WOOD to Port (Bee)'),
(3206, 'Toshiba Carrier to Port'),
(3199, 'Toshiba to Port'),
(3200, 'Toshiba to Port (CJ)'),
(3203, 'Toshiba to Port (Midea)'),
(3233, 'TOSHIBA บางบ่อ to Port'),
(3127, 'TOYO to Port'),
(3055, 'TOYOBO KM 39 to Port'),
(3036, 'TOYOBO to Port'),
(2991, 'TPV to Port'),
(3216, 'UNIC to Port'),
(3220, 'Unico to Port'),
(3209, 'Vicker to Port'),
(3154, 'VILLEROY & BOCH to Port'),
(3077, 'VILLEROY&BOCH to Port'),
(2879, 'VILLEROY(หนองแค) to Port'),
(3210, 'VIRIYA to Port'),
(2923, 'W Evergreen to Port (Bee)'),
(2904, 'WH11 to Port'),
(2900, 'WH13 to Port'),
(2906, 'WH14 to Port'),
(2907, 'WH6 to Port'),
(3156, 'WINTECH MACHINERY to Port'),
(3176, 'WINWAVE to Port'),
(2883, 'WON SINH to Port'),
(3219, 'World Pigment to Port'),
(3244, 'XPO to Port'),
(3022, 'Xtron to Port'),
(3177, 'Y&J to Port'),
(3248, 'YIFAN 1 to Port'),
(3249, 'YIFAN 2 to Port'),
(3250, 'YIFAN 3 to Port'),
(3251, 'YIFAN 4 to Port'),
(3252, 'YIFAN 5 to Port'),
(3253, 'YIFAN 6 to Port'),
(3254, 'YIFAN 7 to Port'),
(3255, 'YIFAN 8 to Port'),
(3256, 'YIFAN 9 to Port'),
(3187, 'Yifan Whale to Port'),
(3246, 'YUSEN to Port'),
(3257, 'Zhongce Rubber to Port'),
(3170, 'ZHONGSHAN ANYE BIOTECH to Port'),
(3131, 'ZICOM CESCO ENG. to Port'),
(3025, 'Zinying to Port'),
(3119, 'นครนายก to BKK Port'),
(2876, 'สนามบินอู่ตะเภา-ค่ายพระมหาเจษฎาราชเจ้า'),
(3124, 'สระบุรี to Port'),
(3126, 'สินธนโชติ to Port'),
(3099, 'เดอะเบสท์ มิตรไมตรี');

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_general_ci NOT NULL,
  `attempt_time` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `login_attempts`
--

INSERT INTO `login_attempts` (`id`, `username`, `ip_address`, `attempt_time`) VALUES
(1, 'admin', '::1', '2025-06-18 18:21:35'),
(2, 'admin', '::1', '2025-06-18 18:21:57'),
(3, 'admin', '::1', '2025-06-18 18:22:48'),
(4, 'admin', '::1', '2025-06-18 18:23:05'),
(5, 'admin', '::1', '2025-06-18 18:24:40'),
(6, 'admin', '::1', '2025-06-18 18:37:42'),
(7, 'admin', '::1', '2025-07-30 15:18:44'),
(8, 'kij', '::1', '2025-07-30 15:25:36'),
(9, 'userkij', '::1', '2025-09-03 14:33:15'),
(10, 'userkij', '::1', '2025-09-03 14:59:05');

-- --------------------------------------------------------

--
-- Table structure for table `return_places`
--

CREATE TABLE `return_places` (
  `id` int NOT NULL,
  `place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `return_places`
--

INSERT INTO `return_places` (`id`, `place`) VALUES
(12, 'KLN'),
(1, 'LCB-A0'),
(2, 'LCB-A2/A3'),
(3, 'LCB-B1'),
(4, 'LCB-B2'),
(5, 'LCB-B3'),
(6, 'LCB-B4'),
(7, 'LCB-B5'),
(8, 'LCB-C1C2'),
(9, 'LCB-C3'),
(10, 'LCB-D1'),
(11, 'LCB-JWD'),
(13, 'PORT-SIAMCOM');

-- --------------------------------------------------------

--
-- Table structure for table `return_users`
--

CREATE TABLE `return_users` (
  `id` int NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `return_users`
--

INSERT INTO `return_users` (`id`, `name`) VALUES
(8, '<br /><b>Deprecated</b>:  htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated in <b>/Applications/MAMP/htdocs/container_system_app_v2/public/edit_job.php</b> on line <b>511</b><br />'),
(1, 'Admin'),
(2, 'User1'),
(3, 'User2'),
(11, 'กิจ');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb3 NOT NULL,
  `password_hash` varchar(255) CHARACTER SET utf8mb3 NOT NULL,
  `role` enum('admin','user') CHARACTER SET utf8mb3 NOT NULL DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password_hash`, `role`, `created_at`) VALUES
(1, 'admin', '$2y$10$GYOuY7rhItk9JVFA4ZLdK.3zsr26TOlfnDgqoQ2noty6tYg0y0TkK', 'admin', '2025-06-18 08:00:55'),
(2, 'userkij', '$2y$10$g/7zJMoxdL7d8Hnin.9Wh.EERMFoPmNOA7RdOH0eIW1aizDLHLBUG', 'user', '2025-06-18 18:44:14'),
(3, 'aom', '$2y$10$.GhIk9OC2WYsT69plvxaxOlKWKULjCBddmm1JDVdm49W8Fbl7yaUu', 'user', '2025-09-03 13:50:23'),
(4, 'keaw', '$2y$10$pcAaLxHmUkRKKCAG0E4jae9y1zrz8Xgt9dZkDpzCDFBJc1oukz9Oe', 'user', '2025-09-03 13:52:14'),
(5, 'mai', '$2y$10$VhRU8AKsJ9P8WkbszNoOne2xpqhe5pkLnBXPzmcvFlfQ5VfY1nOJG', 'user', '2025-09-03 13:52:24');

-- --------------------------------------------------------

--
-- Structure for view `job_edit_summary`
--
DROP TABLE IF EXISTS `job_edit_summary`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `job_edit_summary`  AS SELECT `j`.`id` AS `job_id`, `j`.`job_number` AS `job_number`, `j`.`customer_name` AS `customer_name`, count(`jeh`.`id`) AS `total_edits`, count(distinct `jeh`.`user_id`) AS `unique_editors`, max(`jeh`.`edit_timestamp`) AS `last_edit_time`, (select `jeh2`.`username` from `job_edit_history` `jeh2` where (`jeh2`.`job_id` = `j`.`id`) order by `jeh2`.`edit_timestamp` desc limit 1) AS `last_editor` FROM (`jobs` `j` left join `job_edit_history` `jeh` on((`j`.`id` = `jeh`.`job_id`))) GROUP BY `j`.`id`, `j`.`job_number`, `j`.`customer_name` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `customer_names`
--
ALTER TABLE `customer_names`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `depositor_names`
--
ALTER TABLE `depositor_names`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `deposit_places`
--
ALTER TABLE `deposit_places`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `place` (`place`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `job_edit_history`
--
ALTER TABLE `job_edit_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_job_id` (`job_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_timestamp` (`edit_timestamp`);

--
-- Indexes for table `job_titles`
--
ALTER TABLE `job_titles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `title` (`title`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `return_places`
--
ALTER TABLE `return_places`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `place` (`place`);

--
-- Indexes for table `return_users`
--
ALTER TABLE `return_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `customer_names`
--
ALTER TABLE `customer_names`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=76;

--
-- AUTO_INCREMENT for table `depositor_names`
--
ALTER TABLE `depositor_names`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `deposit_places`
--
ALTER TABLE `deposit_places`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `job_edit_history`
--
ALTER TABLE `job_edit_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `job_titles`
--
ALTER TABLE `job_titles`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3302;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `return_places`
--
ALTER TABLE `return_places`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `return_users`
--
ALTER TABLE `return_users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `jobs`
--
ALTER TABLE `jobs`
  ADD CONSTRAINT `jobs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `job_edit_history`
--
ALTER TABLE `job_edit_history`
  ADD CONSTRAINT `job_edit_history_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `job_edit_history_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
