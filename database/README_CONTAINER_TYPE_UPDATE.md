# Container Type Update Migration

## ปัญหาที่แก้ไข

ระบบเดิมมีปัญหาเรื่อง Container Type ที่ไม่ตรงกันระหว่าง:
- Database Schema: ใช้ ENUM('เปล่า','หนัก')
- Frontend Form: ใช้ค่า 'เปล่าสั้น', 'เปล่ายาว', 'หนักสั้น', 'หนักยาว'
- Validation: ตรวจสอบค่าเก่า 'เปล่า', 'หนัก'

## การแก้ไข

### 1. อัปเดต Database Schema
- เปลี่ยน ENUM จาก `('เปล่า','หนัก')` เป็น `('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว')`
- อัปเดตทั้งใน `schema.sql` และ `container_system.sql`

### 2. อัปเดต PHP Code
- แก้ไข validation ใน `create_job.php`
- แก้ไข service fee calculation ใน `create_job.php` และ `edit_job.php`
- ใช้ switch statement แทน ternary operator

### 3. Service Fee ใหม่
- เปล่าสั้น: 214 บาท
- เปล่ายาว: 350 บาท  
- หนักสั้น: 900 บาท
- หนักยาว: 1200 บาท

## วิธีการ Migration

### สำหรับฐานข้อมูลใหม่
ใช้ไฟล์ `schema.sql` หรือ `container_system.sql` ที่อัปเดตแล้ว

### สำหรับฐานข้อมูลที่มีอยู่แล้ว
1. รันไฟล์ `run_container_type_migration.php` ผ่าน web browser
2. หรือรันไฟล์ SQL โดยตรง: `update_container_type_enum.sql`

### ขั้นตอนการ Migration
1. เพิ่มคอลัมน์ชั่วคราว `container_type_new`
2. แปลงข้อมูลเก่า:
   - 'เปล่า' → 'เปล่ายาว'
   - 'หนัก' → 'หนักยาว'
3. ลบคอลัมน์เก่า
4. เปลี่ยนชื่อคอลัมน์ใหม่
5. อัปเดต service_fee ให้ตรงกับประเภทใหม่

## การทดสอบ

หลังจาก migration แล้ว ให้ทดสอบ:
1. สร้าง job ใหม่ - ตรวจสอบว่าเลือก container type ได้ถูกต้อง
2. แก้ไข job เก่า - ตรวจสอบว่า service fee คำนวณถูกต้อง
3. ตรวจสอบข้อมูลเก่าที่ถูกแปลงแล้ว

## Rollback (ถ้าจำเป็น)

หากต้องการย้อนกลับ:
```sql
-- เพิ่มคอลัมน์เก่า
ALTER TABLE jobs ADD COLUMN container_type_old ENUM('เปล่า','หนัก');

-- แปลงข้อมูลกลับ
UPDATE jobs SET container_type_old = 
    CASE 
        WHEN container_type IN ('เปล่าสั้น','เปล่ายาว') THEN 'เปล่า'
        WHEN container_type IN ('หนักสั้น','หนักยาว') THEN 'หนัก'
        ELSE 'เปล่า'
    END;

-- ลบคอลัมน์ใหม่และเปลี่ยนชื่อ
ALTER TABLE jobs DROP COLUMN container_type;
ALTER TABLE jobs CHANGE container_type_old container_type ENUM('เปล่า','หนัก');
```
