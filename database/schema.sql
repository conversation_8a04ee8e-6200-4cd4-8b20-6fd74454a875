CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    job_number VARCHAR(50) NOT NULL,
    user_id INT NOT NULL,
    job_date DATE DEFAULT CURRENT_TIMESTAMP,  -- A
    customer_name VARCHAR(255),               -- B ชื่อลูกค้า
    user_name VARCHAR(255),                   -- C คนใช้งาน
    job_title VARCHAR(255),                   -- D ชื่องาน
    booking_bl VARCHAR(100),                  -- E Booking/BL
    container_no VARCHAR(11),                 -- F Container no.
    container_size ENUM('20','40'),           -- G Size ตู้
    first_return_date DATE,                   -- H First Return date
    closing_date DATE,                        -- I Closing date
    return_place VARCHAR(255),                -- J สถานที่คืนตู้
    deposit_date DATE,                        -- K ฝากตู้
    deposit_place VARCHAR(255),               -- L สถานที่ฝากตู้
    container_type ENUM('เปล่าสั้น','เปล่ายาว','หนักสั้น','หนักยาว'),     -- M ประเภทตู้
    final_return_date DATE,                   -- N วันที่คืนตู้จริง
    return_user VARCHAR(255),                 -- O คนคืนตู้
    status ENUM('Pending','Done') DEFAULT 'Pending', -- P Status
    service_fee VARCHAR(50),                  -- Q ค่าบริการ
    remark TEXT,                              -- R Remark
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- ตาราง3 autocomplete ชื่อลูกค้า
CREATE TABLE customer_names (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL
);

-- ตาราง3 autocomplete ชื่องาน
CREATE TABLE job_titles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) UNIQUE NOT NULL
);

-- ตาราง3 autocomplete สถาน3 ใช้บ่อย
CREATE TABLE return_places (
    id INT AUTO_INCREMENT PRIMARY KEY,
    place VARCHAR(255) UNIQUE NOT NULL
);

-- ตาราง3 autocomplete สถาน3 ฝากตู้
CREATE TABLE deposit_places (
    id INT AUTO_INCREMENT PRIMARY KEY,
    place VARCHAR(255) UNIQUE NOT NULL
);

-- ตารางความพยายามล็อกล้มเหลว
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time DATETIME DEFAULT NULL
);

-- Default admin user:
INSERT INTO users (username, password_hash, role) VALUES (
    'admin',
    '$2y$10$D9cUKMfr9WW48Xjz1JmH7.tPtQETiHj48SgQQ7bEV6K1eMzt41CCu', -- bcrypt("admin123")
    'admin'
);
