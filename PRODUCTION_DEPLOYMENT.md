# Production Deployment Guide
## Container System Application v2.0

### 🚀 Pre-Deployment Checklist

#### 1. Database Configuration
- [ ] สร้าง database user ใหม่ที่ไม่ใช่ root
- [ ] ตั้งรหัสผ่านที่ปลอดภัยสำหรับ database
- [ ] อัปเดตข้อมูลใน `includes/config.php`
- [ ] ทดสอบการเชื่อมต่อ database

#### 2. Security Settings
- [ ] เปลี่ยน ENVIRONMENT เป็น 'production' ใน `includes/config.php`
- [ ] ตั้งค่า SSL/HTTPS
- [ ] อัปเดต session.cookie_secure เป็น 1 ใน config.php
- [ ] ตรวจสอบ file permissions

#### 3. File Permissions (Linux/Unix)
```bash
# Set proper permissions
chmod 755 public/
chmod 644 public/*.php
chmod 700 includes/
chmod 644 includes/*.php
chmod 700 database/
chmod 644 database/*.sql
chmod 755 logs/
chmod 644 logs/.htaccess
```

#### 4. Web Server Configuration

##### Apache
- ตรวจสอบว่า mod_rewrite เปิดใช้งาน
- ตรวจสอบว่า mod_headers เปิดใช้งาน
- อัปโหลดไฟล์ .htaccess ทั้งหมด

##### Nginx (ตัวอย่าง configuration)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/container_system_app_v2/public;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Deny access to sensitive directories
    location ~ ^/(includes|database|logs)/ {
        deny all;
        return 403;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 🔧 Configuration Steps

#### 1. Database Setup
```sql
-- สร้าง user ใหม่
CREATE USER 'container_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT SELECT, INSERT, UPDATE, DELETE ON container_system.* TO 'container_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 2. Update Configuration
แก้ไขไฟล์ `includes/config.php`:
```php
define('ENVIRONMENT', 'production');
define('DB_USER', 'container_user');
define('DB_PASS', 'secure_password_here');
```

#### 3. SSL Certificate (แนะนำ)
- ติดตั้ง SSL certificate
- อัปเดต .htaccess เพื่อ redirect HTTP เป็น HTTPS
- เปลี่ยน session.cookie_secure เป็น 1

### 🛡️ Security Recommendations

#### 1. Regular Updates
- อัปเดต PHP เป็นเวอร์ชันล่าสุด
- อัปเดต MySQL/MariaDB
- อัปเดต web server

#### 2. Monitoring
- ตั้งค่า log monitoring
- ตรวจสอบ error logs เป็นประจำ
- ติดตาม login attempts

#### 3. Backup Strategy
```bash
# Database backup (ทำทุกวัน)
mysqldump -u container_user -p container_system > backup_$(date +%Y%m%d).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/container_system_app_v2/
```

### 📊 Performance Optimization

#### 1. PHP Configuration
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 30
upload_max_filesize = 10M
post_max_size = 10M
session.gc_maxlifetime = 3600
opcache.enable = 1
```

#### 2. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_jobs_user_id ON jobs(user_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_date ON jobs(job_date);
CREATE INDEX idx_login_attempts_username ON login_attempts(username);
```

### 🔍 Testing Checklist

#### 1. Functionality Tests
- [ ] Login/Logout
- [ ] Create Job
- [ ] Edit Job
- [ ] Delete Job (Admin only)
- [ ] User Management (Admin only)
- [ ] Reports and Export
- [ ] Edit History

#### 2. Security Tests
- [ ] SQL Injection attempts
- [ ] XSS attempts
- [ ] CSRF protection
- [ ] File access restrictions
- [ ] Session security

#### 3. Performance Tests
- [ ] Page load times
- [ ] Database query performance
- [ ] File upload functionality

### 🚨 Emergency Procedures

#### 1. Rollback Plan
- เก็บ backup ของเวอร์ชันเก่า
- มี database backup พร้อม restore
- เตรียม maintenance page

#### 2. Contact Information
- Database Administrator: [contact]
- System Administrator: [contact]
- Developer: [contact]

### 📝 Post-Deployment

#### 1. Immediate Actions
- [ ] ทดสอบ login ด้วย admin account
- [ ] ตรวจสอบ error logs
- [ ] ทดสอบฟีเจอร์หลักทั้งหมด

#### 2. Monitoring Setup
- [ ] ตั้งค่า log rotation
- [ ] ตั้งค่า monitoring alerts
- [ ] ตั้งค่า automated backups

---

**หมายเหตุ:** ก่อนขึ้น production ควรทดสอบในสภาพแวดล้อม staging ที่เหมือนกับ production ก่อน
