#!/bin/bash
# Script to fix file permissions for Container System on Plesk/cPanel

echo "Fixing file permissions for Container System..."

# Fix root directory permissions first
chmod 755 .

# Set directory permissions (all directories must be executable)
find . -type d -exec chmod 755 {} \;

# Set file permissions (all files readable)
find . -type f -exec chmod 644 {} \;

# Critical: Make sure root directory is executable
chmod 755 .

# Public directory must be accessible
chmod 755 public/
chmod 644 public/*.php
chmod 644 public/.htaccess

# Includes directory should be accessible but not browsable
chmod 755 includes/
chmod 644 includes/*.php
chmod 644 includes/.htaccess

# Database directory should be accessible but not browsable
chmod 755 database/
chmod 644 database/*.sql
chmod 644 database/.htaccess

# Logs directory
chmod 755 logs/
chmod 644 logs/.htaccess

# Make sure all .htaccess files are readable
find . -name ".htaccess" -exec chmod 644 {} \;

echo "Permissions fixed!"
echo ""
echo "Root directory permissions:"
ls -ld .

echo ""
echo "Directory structure:"
ls -la

echo ""
echo "Public directory:"
ls -la public/

echo ""
echo "All .htaccess files:"
find . -name ".htaccess" -exec ls -la {} \;
