#!/bin/bash
# <PERSON>ript to fix file permissions for Container System

echo "Fixing file permissions for Container System..."

# Set directory permissions
find . -type d -exec chmod 755 {} \;

# Set file permissions
find . -type f -exec chmod 644 {} \;

# Set specific permissions for directories
chmod 755 public/
chmod 755 includes/
chmod 755 database/
chmod 755 logs/

# Set specific permissions for PHP files
chmod 644 public/*.php
chmod 644 includes/*.php

# Set specific permissions for sensitive directories
chmod 700 includes/
chmod 700 database/
chmod 700 logs/

# Make sure web server can read public directory
chmod 755 public/
chmod 644 public/*.php
chmod 644 public/.htaccess

# Fix ownership (uncomment and modify if needed)
# chown -R www-data:www-data .
# chown -R apache:apache .

echo "Permissions fixed!"
echo "Directory structure:"
ls -la

echo ""
echo "Public directory:"
ls -la public/

echo ""
echo "Includes directory:"
ls -la includes/
